# ==============================================================================
# OUTPUTS
# ==============================================================================
# Output values from all modules for reference and integration

# output "core_outputs" {
#   description = "Outputs from the core module (Management Groups and Policies)"
#   value = module.core
# }

output "connectivity_outputs" {
  description = "Outputs from the connectivity module"
  value = module.connectivity
}

output "management_outputs" {
  description = "Outputs from the management module"
  value = module.management
  sensitive = true
}

output "spokes_outputs" {
  description = "Outputs from all deployed spoke networks"
  value = var.deploy_spokes ? module.spokes[0] : null
}