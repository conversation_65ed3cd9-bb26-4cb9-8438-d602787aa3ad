{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "Deny-HybridNetworking", "dependsOn": [], "properties": {"description": "Denies deployment of vWAN/ER/VPN gateway resources in the Corp landing zone.", "displayName": "Deny the deployment of vWAN/ER/VPN gateway resources", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6c112d4e-5bc7-47ae-a041-ea2d9dccd749", "enforcementMode": "<PERSON><PERSON><PERSON>", "nonComplianceMessages": [{"message": "vWAN/ER/VPN gateway resources {enforcementMode} not be deployed in the Corp landing zone."}], "parameters": {"listOfResourceTypesNotAllowed": {"value": ["microsoft.network/expressroutecircuits", "microsoft.network/expressroutegateways", "microsoft.network/expressrouteports", "microsoft.network/virtualwans", "microsoft.network/virtualhubs", "microsoft.network/vpngateways", "microsoft.network/p2svpngateways", "microsoft.network/vpnsites", "microsoft.network/virtualnetworkgateways"]}, "effect": {"value": "<PERSON><PERSON>"}}, "scope": "${current_scope_resource_id}", "notScopes": []}, "location": "${default_location}", "identity": {"type": "None"}}