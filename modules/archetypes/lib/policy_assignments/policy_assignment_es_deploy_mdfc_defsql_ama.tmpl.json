{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "Deploy-MDFC-DefSQL-AMA", "location": "${default_location}", "dependsOn": [], "identity": {"type": "SystemAssigned"}, "properties": {"description": "Microsoft Defender for SQL collects events from the agents and uses them to provide security alerts and tailored hardening tasks (recommendations).", "displayName": "Enable Defender for SQL on SQL VMs and Arc-enabled SQL Servers", "policyDefinitionId": "/providers/Microsoft.Authorization/policySetDefinitions/de01d381-bae9-4670-8870-786f89f49e26", "enforcementMode": "<PERSON><PERSON><PERSON>", "nonComplianceMessages": [{"message": "Microsoft Defender for SQL {enforcementMode} be deployed."}], "parameters": {"workspaceRegion": {"value": "${default_location}"}, "userWorkspaceResourceId": {"value": "/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/${root_scope_id}-mgmt/providers/Microsoft.OperationalInsights/workspaces/${root_scope_id}-la"}, "bringYourOwnDcr": {"value": true}, "dcrResourceId": {"value": "${azure_monitor_data_collection_rule_sql_resource_id}"}, "enableCollectionOfSqlQueriesForSecurityResearch": {"value": false}, "bringYourOwnUserAssignedManagedIdentity": {"value": true}, "userAssignedIdentityResourceId": {"value": "${user_assigned_managed_identity_resource_id}"}}, "scope": "${current_scope_resource_id}", "notScopes": []}}