{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "Deploy-ASC-Monitoring", "location": "${default_location}", "dependsOn": [], "identity": {"type": "SystemAssigned"}, "properties": {"description": "Microsoft Cloud Security Benchmark policy initiative.", "displayName": "Microsoft Cloud Security Benchmark", "policyDefinitionId": "/providers/Microsoft.Authorization/policySetDefinitions/1f3afdf9-d0c9-4c3d-847f-89da613e70a8", "enforcementMode": "<PERSON><PERSON><PERSON>", "nonComplianceMessages": [{"message": "Microsoft Cloud Security Benchmark {enforcementMode} be met."}], "parameters": {}, "scope": "${current_scope_resource_id}", "notScopes": []}}