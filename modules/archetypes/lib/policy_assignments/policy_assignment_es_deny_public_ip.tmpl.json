{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "Deny-Public-IP", "dependsOn": [], "properties": {"description": "This policy denies creation of Public IPs under the assigned scope.", "displayName": "Deny the creation of public IP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6c112d4e-5bc7-47ae-a041-ea2d9dccd749", "enforcementMode": "<PERSON><PERSON><PERSON>", "nonComplianceMessages": [{"message": "Public IPs {enforcementMode} not be created under this scope."}], "parameters": {"listOfResourceTypesNotAllowed": {"value": ["Microsoft.Network/publicIPAddresses"]}, "effect": {"value": "<PERSON><PERSON>"}}, "scope": "${current_scope_resource_id}", "notScopes": []}, "location": "${default_location}", "identity": {"type": "None"}}