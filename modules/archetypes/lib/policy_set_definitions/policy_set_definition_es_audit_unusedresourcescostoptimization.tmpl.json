{"name": "Audit-UnusedResourcesCostOptimization", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Unused resources driving cost should be avoided", "description": "Optimize cost by detecting unused but chargeable resources. Leverage this Azure Policy Initiative as a cost control tool to reveal orphaned resources that are contributing cost.", "metadata": {"version": "2.0.0", "category": "Cost Optimization", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effectDisks": {"type": "String", "metadata": {"displayName": "Disks Effect", "description": "Enable or disable the execution of the policy for Microsoft.Compute/disks"}, "allowedValues": ["Audit", "Disabled"], "defaultValue": "Audit"}, "effectPublicIpAddresses": {"type": "String", "metadata": {"displayName": "PublicIpAddresses Effect", "description": "Enable or disable the execution of the policy for Microsoft.Network/publicIpAddresses"}, "allowedValues": ["Audit", "Disabled"], "defaultValue": "Audit"}, "effectServerFarms": {"type": "String", "metadata": {"displayName": "ServerFarms Effect", "description": "Enable or disable the execution of the policy for Microsoft.Web/serverfarms"}, "allowedValues": ["Audit", "Disabled"], "defaultValue": "Audit"}}, "policyDefinitions": [{"policyDefinitionReferenceId": "AuditDisksUnusedResourcesCostOptimization", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Audit-Disks-UnusedResourcesCostOptimization", "parameters": {"effect": {"value": "[parameters('effectDisks')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "AuditPublicIpAddressesUnusedResourcesCostOptimization", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Audit-PublicIpAddresses-UnusedResourcesCostOptimization", "parameters": {"effect": {"value": "[parameters('effectPublicIpAddresses')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "AuditServerFarmsUnusedResourcesCostOptimization", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Audit-ServerFarms-UnusedResourcesCostOptimization", "parameters": {"effect": {"value": "[parameters('effectServerFarms')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "AuditAzureHybridBenefitUnusedResourcesCostOptimization", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Audit-AzureHybridBenefit", "parameters": {"effect": {"value": "Audit"}}, "groupNames": []}], "policyDefinitionGroups": null}}