{"name": "Deploy-MDFC-Config_20240319", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Deploy Microsoft Defender for Cloud configuration", "description": "Deploy Microsoft Defender for Cloud configuration", "metadata": {"version": "2.1.0", "category": "Security Center", "source": "https://github.com/Azure/Enterprise-Scale/", "replacesPolicy": "Deploy-MDFC-Config", "alzCloudEnvironments": ["AzureCloud"]}, "parameters": {"emailSecurityContact": {"type": "string", "metadata": {"displayName": "Security contacts email address", "description": "Provide email address for Microsoft Defender for Cloud contact details"}}, "minimalSeverity": {"type": "string", "allowedValues": ["High", "Medium", "Low"], "defaultValue": "High", "metadata": {"displayName": "Minimal severity", "description": "Defines the minimal alert severity which will be sent as email notifications"}}, "logAnalytics": {"type": "String", "metadata": {"displayName": "Primary Log Analytics workspace", "description": "Select Log Analytics workspace from dropdown list. If this workspace is outside of the scope of the assignment you must manually grant 'Log Analytics Contributor' permissions (or similar) to the policy assignment's principal ID.", "strongType": "omsWorkspace"}}, "ascExportResourceGroupName": {"type": "String", "metadata": {"displayName": "Resource Group name for the export to Log Analytics workspace configuration", "description": "The resource group name where the export to Log Analytics workspace configuration is created. If you enter a name for a resource group that doesn't exist, it'll be created in the subscription. Note that each resource group can only have one export to Log Analytics workspace configured."}}, "ascExportResourceGroupLocation": {"type": "String", "metadata": {"displayName": "Resource Group location for the export to Log Analytics workspace configuration", "description": "The location where the resource group and the export to Log Analytics workspace configuration are created."}}, "createResourceGroup": {"type": "Boolean", "metadata": {"displayName": "Create resource group", "description": "If a resource group does not exists in the scope, a new resource group will be created. If the resource group exists and this flag is set to 'true' the policy will re-deploy the resource group. Please note this will reset any Azure Tag on the resource group."}, "defaultValue": true, "allowedValues": [true, false]}, "enableAscForCosmosDbs": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForSql": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForSqlOnVm": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForArm": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForOssDb": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForAppServices": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForKeyVault": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForStorage": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForContainers": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForServers": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForServersVulnerabilityAssessments": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "vulnerabilityAssessmentProvider": {"type": "String", "allowedValues": ["default", "mdeTvm"], "defaultValue": "mdeTvm", "metadata": {"displayName": "Vulnerability assessment provider type", "description": "Select the vulnerability assessment solution to provision to machines."}}, "enableAscForCspm": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}}, "policyDefinitions": [{"policyDefinitionReferenceId": "defenderForOssDb", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/44433aa3-7ec2-4002-93ea-65c65ff0310a", "parameters": {"effect": {"value": "[parameters('enableAscForOssDb')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "defenderForVM", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/8e86a5b6-b9bd-49d1-8e21-4bb8a0862222", "parameters": {"effect": {"value": "[parameters('enableAscForServers')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "defenderForVMVulnerabilityAssessment", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/13ce0167-8ca6-4048-8e6b-f996402e3c1b", "parameters": {"effect": {"value": "[parameters('enableAscForServersVulnerabilityAssessments')]"}, "vaType": {"value": "[parameters('vulnerabilityAssessmentProvider')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "defenderForSqlServerVirtualMachines", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/50ea7265-7d8c-429e-9a7d-ca1f410191c3", "parameters": {"effect": {"value": "[parameters('enableAscForSqlOnVm')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "defenderForAppServices", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b40e7bcd-a1e5-47fe-b9cf-2f534d0bfb7d", "parameters": {"effect": {"value": "[parameters('enableAscForAppServices')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "defenderForStorageAccountsV2", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/cfdc5972-75b3-4418-8ae1-7f5c36839390", "parameters": {"effect": {"value": "[parameters('enableAscForStorage')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "defender<PERSON><PERSON><PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c9ddb292-b203-4738-aead-18e2716e858f", "parameters": {"effect": {"value": "[parameters('enableAscForContainers')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "defender<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/64def556-fbad-4622-930e-72d1d5589bf5", "parameters": {"effect": {"value": "[parameters('enableAscForContainers')]"}, "logAnalyticsWorkspaceResourceId": {"value": "[parameters('logAnalytics')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "azurePolicyForKubernetes", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a8eff44f-8c92-45c3-a3fb-9880802d67a7", "parameters": {"effect": {"value": "[parameters('enableAscForContainers')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1f725891-01c0-420a-9059-4fa46cb770b7", "parameters": {"effect": {"value": "[parameters('enableAscForKeyVault')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "defender<PERSON><PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b7021b2b-08fd-4dc0-9de7-3c6ece09faf9", "parameters": {"effect": {"value": "[parameters('enableAscForArm')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "defender<PERSON>or<PERSON>qlP<PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b99b73e7-074b-4089-9395-b7236f094491", "parameters": {"effect": {"value": "[parameters('enableAscForSql')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "defenderForCosmosDbs", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/82bf5b87-728b-4a74-ba4d-6123845cf542", "parameters": {"effect": {"value": "[parameters('enableAscForCosmosDbs')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "defender<PERSON><PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/72f8cee7-2937-403d-84a1-a4e3e57f3c21", "parameters": {"effect": {"value": "[parameters('enableAscForCspm')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "securityEmailContact", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deploy-ASC-SecurityContacts", "parameters": {"emailSecurityContact": {"value": "[parameters('emailSecurityContact')]"}, "minimalSeverity": {"value": "[parameters('minimalSeverity')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "ascExport", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ffb6f416-7bd2-4488-8828-56585fef2be9", "parameters": {"resourceGroupName": {"value": "[parameters('ascExportResourceGroupName')]"}, "resourceGroupLocation": {"value": "[parameters('ascExportResourceGroupLocation')]"}, "createResourceGroup": {"value": "[parameters('createResourceGroup')]"}, "workspaceResourceId": {"value": "[parameters('logAnalytics')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "migrateToMdeTvm", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/766e621d-ba95-4e43-a6f2-e945db3d7888", "parameters": {}, "groupNames": []}], "policyDefinitionGroups": null}}