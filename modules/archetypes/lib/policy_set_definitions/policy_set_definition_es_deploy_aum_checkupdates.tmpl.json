{"name": "Deploy-AUM-CheckUpdates", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Configure periodic checking for missing system updates on azure virtual machines and Arc-enabled virtual machines", "description": "Configure auto-assessment (every 24 hours) for OS updates. You can control the scope of assignment according to machine subscription, resource group, location or tag. Learn more about this for Windows: https://aka.ms/computevm-windowspatchassessmentmode, for Linux: https://aka.ms/computevm-linuxpatchassessmentmode.", "metadata": {"version": "1.0.0", "category": "Security Center", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud"]}, "parameters": {"assessmentMode": {"type": "String", "metadata": {"displayName": "Assessment mode", "description": "Assessment mode for the machines."}, "allowedValues": ["ImageDefault", "AutomaticByPlatform"], "defaultValue": "AutomaticByPlatform"}, "locations": {"type": "Array", "metadata": {"displayName": "Machines locations", "description": "The list of locations from which machines need to be targeted.", "strongType": "location"}, "defaultValue": []}, "tagValues": {"type": "Object", "metadata": {"displayName": "Tags on machines", "description": "The list of tags that need to matched for getting target machines."}, "defaultValue": {}}, "tagOperator": {"type": "String", "metadata": {"displayName": "Tag operator", "description": "Matching condition for resource tags"}, "allowedValues": ["All", "Any"], "defaultValue": "Any"}}, "policyDefinitions": [{"policyDefinitionReferenceId": "azureUpdateManagerVmCheckUpdateWindows", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/59efceea-0c96-497e-a4a1-4eb2290dac15", "parameters": {"assessmentMode": {"value": "[parameters('assessmentMode')]"}, "osType": {"value": "Windows"}, "locations": {"value": "[parameters('locations')]"}, "tagValues": {"value": "[parameters('tagValues')]"}, "tagOperator": {"value": "[parameters('tagOperator')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "azureUpdateManagerVmCheckUpdateLinux", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/59efceea-0c96-497e-a4a1-4eb2290dac15", "parameters": {"assessmentMode": {"value": "[parameters('assessmentMode')]"}, "osType": {"value": "Linux"}, "locations": {"value": "[parameters('locations')]"}, "tagValues": {"value": "[parameters('tagValues')]"}, "tagOperator": {"value": "[parameters('tagOperator')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "azureUpdateManagerVmArcCheckUpdateWindows", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/bfea026e-043f-4ff4-9d1b-bf301ca7ff46", "parameters": {"assessmentMode": {"value": "[parameters('assessmentMode')]"}, "osType": {"value": "Windows"}, "locations": {"value": "[parameters('locations')]"}, "tagValues": {"value": "[parameters('tagValues')]"}, "tagOperator": {"value": "[parameters('tagOperator')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "azureUpdateManagerVmArcCheckUpdateLinux", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/bfea026e-043f-4ff4-9d1b-bf301ca7ff46", "parameters": {"assessmentMode": {"value": "[parameters('assessmentMode')]"}, "osType": {"value": "Linux"}, "locations": {"value": "[parameters('locations')]"}, "tagValues": {"value": "[parameters('tagValues')]"}, "tagOperator": {"value": "[parameters('tagOperator')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}