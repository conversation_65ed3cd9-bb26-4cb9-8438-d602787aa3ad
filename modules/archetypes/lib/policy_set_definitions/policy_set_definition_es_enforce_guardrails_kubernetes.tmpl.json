{"name": "Enforce-Guardrails-Kubernetes", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Kubernetes", "description": "This policy initiative is a group of policies that ensures Kubernetes is compliant per regulated Landing Zones.", "metadata": {"version": "1.1.0", "category": "Kubernetes", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"aksKms": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"]}, "aksCni": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"]}, "aksLocalAuth": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "aksPrivateCluster": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "aksPolicy": {"type": "string", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"]}, "aksCommandInvoke": {"type": "string", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"]}, "aksReadinessOrLivenessProbes": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "aksPrivContainers": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"]}, "aksPrivEscalation": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"]}, "aksAllowedCapabilities": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"]}, "aksTempDisk": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "aksInternalLb": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"]}, "aksDefaultNamespace": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"]}, "aksNakedPods": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "aksShareHostProcessAndNamespace": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"]}, "aksWindowsContainerAdministrator": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionReferenceId": "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON>-Container-Administrator", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/5485eac0-7e8f-4964-998b-a44f4f0c1e75", "parameters": {"effect": {"value": "[parameters('aksWindowsContainerAdministrator')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-<PERSON>ks-Shared-Host-Process-Namespace", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/47a1ee2f-2a2a-4576-bf2a-e0e36709c2b8", "parameters": {"effect": {"value": "[parameters('aksShareHostProcessAndNamespace')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Aks-Naked-Pods", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/65280eef-c8b4-425e-9aec-af55e55bf581", "parameters": {"effect": {"value": "[parameters('aksNakedPods')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-<PERSON><PERSON>-De<PERSON>ult-Namespace", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/9f061a12-e40d-4183-a00e-171812443373", "parameters": {"effect": {"value": "[parameters('aksDefaultNamespace')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Aks-Internal-Lb", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/3fc4dc25-5baf-40d8-9b05-7fe74c1bc64e", "parameters": {"effect": {"value": "[parameters('aksInternalLb')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Aks-Temp-Disk-Encryption", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/41425d9f-d1a5-499a-9932-f8ed8453932c", "parameters": {"effect": {"value": "[parameters('aksTempDisk')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-<PERSON>ks-Allowed-Capabilities", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c26596ff-4d70-4e6a-9a30-c2506bd2f80c", "parameters": {"effect": {"value": "[parameters('aksAllowedCapabilities')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Aks-Priv-Escalation", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1c6e92c9-99f0-4e55-9cf2-0c234dc48f99", "parameters": {"effect": {"value": "[parameters('aksPrivEscalation')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Aks-Priv-Containers", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/95edb821-ddaf-4404-9732-666045e056b4", "parameters": {"effect": {"value": "[parameters('aksPrivContainers')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-<PERSON>ks-ReadinessOrLiveness-Probes", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b1a9997f-2883-4f12-bdff-2280f99b5915", "parameters": {"effect": {"value": "[parameters('aksReadinessOrLivenessProbes')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Dine-Aks-Command-Invoke", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1b708b0a-3380-40e9-8b79-821f9fa224cc", "parameters": {"effect": {"value": "[parameters('aksCommandInvoke')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Dine-Aks-Policy", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a8eff44f-8c92-45c3-a3fb-9880802d67a7", "parameters": {"effect": {"value": "[parameters('aksPolicy')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Aks-Private-Cluster", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/040732e8-d947-40b8-95d6-854c95024bf8", "parameters": {"effect": {"value": "[parameters('aksPrivateCluster')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Aks-Local-Auth", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/993c2fcd-2b29-49d2-9eb0-df2c3a730c32", "parameters": {"effect": {"value": "[parameters('aksLocalAuth')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Aks-Kms", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/dbbdc317-9734-4dd8-9074-993b29c69008", "parameters": {"effect": {"value": "[parameters('aksKms')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Aks-<PERSON>ni", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/46238e2f-3f6f-4589-9f3f-77bed4116e67", "parameters": {"effect": {"value": "[parameters('aksCni')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}