{"name": "Enforce-Guardrails-Automation", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Automation Account", "description": "This policy initiative is a group of policies that ensures Automation Account is compliant per regulated Landing Zones.", "metadata": {"version": "1.0.0", "category": "Automation", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"aaModifyLocalAuth": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "aaVariablesEncryption": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "Disabled", "<PERSON><PERSON>"]}, "aaLocalAuth": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "Disabled", "<PERSON><PERSON>"]}, "aaManagedIdentity": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"]}, "autoHotPatch": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "Disabled", "<PERSON><PERSON>"]}, "aaModifyPublicNetworkAccess": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionReferenceId": "Deny-Windows-Vm-HotPatch", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6d02d2f7-e38b-4bdc-96f3-adc0a8726abc", "parameters": {"effect": {"value": "[parameters('autoHotPatch')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "<PERSON><PERSON><PERSON><PERSON><PERSON>-Managed-Identity", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/dea83a72-443c-4292-83d5-54a2f98749c0", "parameters": {"effect": {"value": "[parameters('aaManagedIdentity')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Aa-Local-Auth", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/48c5f1cb-14ad-4797-8e3b-f78ab3f8d700", "parameters": {"effect": {"value": "[parameters('aaLocalAuth')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Aa-Variables-Encrypt", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/3657f5a0-770e-44a3-b44e-9431ba1e9735", "parameters": {"effect": {"value": "[parameters('aaVariablesEncryption')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Modify-Aa-Local-Auth", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/30d1d58e-8f96-47a5-8564-499a3f3cca81", "parameters": {"effect": {"value": "[parameters('aaModifyLocalAUth')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Modify-Aa-Public-Network-Access", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/23b36a7c-9d26-4288-a8fd-c1d2fa284d8c", "parameters": {"effect": {"value": "[parameters('aaModifyPublicNetworkAccess')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}