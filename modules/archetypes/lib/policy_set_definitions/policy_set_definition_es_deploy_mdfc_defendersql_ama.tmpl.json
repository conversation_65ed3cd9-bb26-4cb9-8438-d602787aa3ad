{"name": "Deploy-MDFC-DefenderSQL-AMA", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "[Deprecated]: Configure SQL VM and Arc-enabled SQL Servers to install Microsoft Defender for SQL and AMA with a user-defined LAW", "description": "Initiative is deprecated as the built-in initiative now supports bringing your own UAMI and DCR. Superseded by https://www.azadvertizer.net/azpolicyinitiativesadvertizer/de01d381-bae9-4670-8870-786f89f49e26.html", "metadata": {"version": "1.0.1-deprecated", "category": "Security Center", "source": "https://github.com/Azure/Enterprise-Scale/", "deprecated": true, "supersededBy": "de01d381-bae9-4670-8870-786f89f49e26", "alzCloudEnvironments": ["AzureCloud"]}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists"}, "workspaceRegion": {"type": "String", "metadata": {"displayName": "Workspace region", "description": "Region of the Log Analytics workspace destination for the Data Collection Rule.", "strongType": "location"}}, "dcrName": {"type": "String", "metadata": {"displayName": "Data Collection Rule Name", "description": "Name of the Data Collection Rule."}}, "dcrResourceGroup": {"type": "String", "metadata": {"displayName": "Data Collection Rule Resource Group", "description": "Resource Group of the Data Collection Rule."}}, "dcrId": {"type": "String", "metadata": {"displayName": "Data Collection Rule Id", "description": "Id of the Data Collection Rule."}}, "userWorkspaceResourceId": {"type": "String", "metadata": {"displayName": "Workspace Resource Id", "description": "Workspace resource Id of the Log Analytics workspace destination for the Data Collection Rule.", "strongType": "omsWorkspace"}}, "enableCollectionOfSqlQueriesForSecurityResearch": {"type": "Boolean", "metadata": {"displayName": "Enable collection of SQL queries for security research", "description": "Enable or disable the collection of SQL queries for security research."}, "allowedValues": [true, false], "defaultValue": false}, "identityResourceGroup": {"type": "String", "metadata": {"displayName": "Identity Resource Group", "description": "The name of the resource group created by the policy."}, "defaultValue": ""}, "userAssignedIdentityName": {"type": "String", "metadata": {"displayName": "User Assigned Managed Identity Name", "description": "The name of the user assigned managed identity."}, "defaultValue": ""}}, "policyDefinitions": [{"policyDefinitionReferenceId": "defenderForSqlArcAma", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/3592ff98-9787-443a-af59-4505d0fe0786", "parameters": {"effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "defenderForSqlArcMdsql", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/65503269-6a54-4553-8a28-0065a8e6d929", "parameters": {"effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "defenderForSqlArcMdsqlDcr", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deploy-MDFC-Arc-Sql-DefenderSQL-DCR", "parameters": {"effect": {"value": "[parameters('effect')]"}, "userWorkspaceResourceId": {"value": "[parameters('userWorkspaceResourceId')]"}, "workspaceRegion": {"value": "[parameters('workspaceRegion')]"}, "enableCollectionOfSqlQueriesForSecurityResearch": {"value": "[parameters('enableCollectionOfSqlQueriesForSecurityResearch')]"}, "dcrName": {"value": "[parameters('dcrName')]"}, "dcrResourceGroup": {"value": "[parameters('dcrResourceGroup')]"}, "dcrId": {"value": "[parameters('dcrId')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "defenderForSqlArcDcrAssociation", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deploy-MDFC-Arc-SQL-DCR-Association", "parameters": {"effect": {"value": "[parameters('effect')]"}, "workspaceRegion": {"value": "[parameters('workspaceRegion')]"}, "dcrName": {"value": "[parameters('dcrName')]"}, "dcrResourceGroup": {"value": "[parameters('dcrResourceGroup')]"}, "dcrId": {"value": "[parameters('dcrId')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "defenderForSql<PERSON>ma", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deploy-MDFC-SQL-AMA", "parameters": {"effect": {"value": "[parameters('effect')]"}, "identityResourceGroup": {"value": "[parameters('identityResourceGroup')]"}, "userAssignedIdentityName": {"value": "[parameters('userAssignedIdentityName')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "defenderForSqlMdsql", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deploy-MDFC-SQL-DefenderSQL", "parameters": {"effect": {"value": "[parameters('effect')]"}, "workspaceRegion": {"value": "[parameters('workspaceRegion')]"}, "dcrResourceGroup": {"value": "[parameters('dcrResourceGroup')]"}, "dcrName": {"value": "[parameters('dcrName')]"}, "dcrId": {"value": "[parameters('dcrId')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "defenderForSqlMdsqlDcr", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deploy-MDFC-SQL-DefenderSQL-DCR", "parameters": {"effect": {"value": "Disabled"}, "userWorkspaceResourceId": {"value": "[parameters('userWorkspaceResourceId')]"}, "workspaceRegion": {"value": "[parameters('workspaceRegion')]"}, "enableCollectionOfSqlQueriesForSecurityResearch": {"value": "[parameters('enableCollectionOfSqlQueriesForSecurityResearch')]"}, "dcrName": {"value": "[parameters('dcrName')]"}, "dcrResourceGroup": {"value": "[parameters('dcrResourceGroup')]"}, "dcrId": {"value": "[parameters('dcrId')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}