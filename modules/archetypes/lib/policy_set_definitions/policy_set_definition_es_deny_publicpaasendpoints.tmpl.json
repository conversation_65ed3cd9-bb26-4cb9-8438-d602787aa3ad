{"name": "Deny-PublicPaaSEndpoints", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Public network access should be disabled for PaaS services", "description": "This policy initiative is a group of policies that prevents creation of Azure PaaS services with exposed public endpoints", "metadata": {"version": "5.1.0", "category": "Network", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud"]}, "parameters": {"CosmosPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access should be disabled for CosmosDB", "description": "This policy denies that Cosmos database accounts  are created with out public network access is disabled."}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "KeyVaultPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access should be disabled for KeyVault", "description": "This policy denies creation of Key Vaults with IP Firewall exposed to all public endpoints"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "SqlServerPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access on Azure SQL Database should be disabled", "description": "This policy denies creation of Sql servers with exposed public endpoints"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "StoragePublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access onStorage accounts should be disabled", "description": "This policy denies creation of storage accounts with IP Firewall exposed to all public endpoints"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "AKSPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access on AKS API should be disabled", "description": "This policy denies  the creation of  Azure Kubernetes Service non-private clusters"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "ACRPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access on Azure Container Registry disabled", "description": "This policy denies the creation of Azure Container Registries with exposed public endpoints "}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "AFSPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access on Azure File Sync disabled", "description": "This policy denies the creation of Azure File Sync instances with exposed public endpoints "}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "PostgreSQLFlexPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access should be disabled for PostgreSql Flexible Server", "description": "This policy denies creation of PostgreSQL Flexible DB accounts with exposed public endpoints"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "postgreSqlPublicNetworkAccess": {"type": "string", "metadata": {"displayName": "Public network access should be disabled for PostgreSQL servers", "description": "This policy denies creation of PostgreSQL DB accounts with exposed public endpoints"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "MySQLFlexPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access should be disabled for MySQL Flexible Server", "description": "This policy denies creation of MySql Flexible Server DB accounts with exposed public endpoints"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "BatchPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access should be disabled for Azure Batch Instances", "description": "This policy denies creation of Azure Batch Instances with exposed public endpoints"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "MariaDbPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access should be disabled for Azure MariaDB", "description": "This policy denies creation of Azure MariaDB with exposed public endpoints"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "MlPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access should be disabled for Azure Machine Learning", "description": "This policy denies creation of Azure Machine Learning with exposed public endpoints"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "RedisCachePublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access should be disabled for Azure Cache for Redis", "description": "This policy denies creation of Azure Cache for Redis with exposed public endpoints"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "BotServicePublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access should be disabled for Bot Service", "description": "This policy denies creation of Bot Service with exposed public endpoints. Bots should be set to 'isolated only' mode"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "AutomationPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access should be disabled for Automation accounts", "description": "This policy denies creation of Automation accounts with exposed public endpoints. Bots should be set to 'isolated only' mode"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "AppConfigPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access should be disabled for App Configuration", "description": "This policy denies creation of App Configuration with exposed public endpoints"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "FunctionPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access should be disabled for Function apps", "description": "This policy denies creation of Function apps with exposed public endpoints"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "FunctionAppSlotPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access should be disabled for Function apps", "description": "This policy denies creation of Function apps with exposed public endpoints"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "AsePublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access should be disabled for App Service Environment apps", "description": "This policy denies creation of App Service Environment apps with exposed public endpoints"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "AsPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access should be disabled for App Service apps", "description": "This policy denies creation of App Service apps with exposed public endpoints"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "ApiManPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access should be disabled for API Management services", "description": "This policy denies creation of API Management services with exposed public endpoints"}, "allowedValues": ["AuditIfNotExists", "Disabled"], "defaultValue": "AuditIfNotExists"}, "ContainerAppsEnvironmentDenyEffect": {"type": "String", "metadata": {"displayName": "Container Apps environment should disable public network access", "description": "This policy denies creation of Container Apps Environment with exposed public endpoints"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "AsrVaultDenyEffect": {"type": "String", "metadata": {"displayName": "Azure Recovery Services vaults should disable public network access", "description": "This policy denies creation of Azure Recovery Services vaults with exposed public endpoints"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "logicAppPublicNetworkAccessEffect": {"type": "String", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "appSlotsPublicNetworkAccess": {"type": "string", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "cognitiveSearchPublicNetworkAccess": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "managedDiskPublicNetworkAccess": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"]}, "containerAppsPublicNetworkAccess": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "adxPublicNetworkAccess": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "adfPublicNetworkAccess": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "eventGridPublicNetworkAccess": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "eventGridTopicPublicNetworkAccess": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "eventHubNamespacesPublicNetworkAccess": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "keyVaultManagedHsmDisablePublicNetwork": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "mySqlPublicNetworkAccess": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "cognitiveServicesNetworkAccess": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "cognitiveServicesPublicNetworkAccess": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "serviceBusDisablePublicNetworkAccess": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "sqlManagedPublicNetworkAccess": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageAccountsPublicAccess": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "synapsePublicNetworkAccess": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "avdHostPoolPublicNetworkAccess": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "avdWorkspacePublicNetworkAccess": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "grafanaPublicNetworkAccess": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionReferenceId": "CosmosDenyPaasPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/797b37f7-06b8-444c-b1ad-fc62867f335a", "parameters": {"effect": {"value": "[parameters('CosmosPublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "KeyVaultDenyPaasPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/405c5871-3e91-4644-8a63-58e19d68ff5b", "parameters": {"effect": {"value": "[parameters('KeyVaultPublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "SqlServerDenyPaasPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1b8ca024-1d5c-4dec-8995-b1a932b41780", "parameters": {"effect": {"value": "[parameters('SqlServerPublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "StorageDenyPaasPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b2982f36-99f2-4db5-8eff-283140c09693", "parameters": {"effect": {"value": "[parameters('StoragePublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "AKSDenyPaasPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/040732e8-d947-40b8-95d6-854c95024bf8", "parameters": {"effect": {"value": "[parameters('AKSPublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "ACRDenyPaasPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0fdf0491-d080-4575-b627-ad0e843cba0f", "parameters": {"effect": {"value": "[parameters('ACRPublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "AFSDenyPaasPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/21a8cd35-125e-4d13-b82d-2e19b7208bb7", "parameters": {"effect": {"value": "[parameters('AFSPublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "PostgreSQLFlexDenyPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/5e1de0e3-42cb-4ebc-a86d-61d0c619ca48", "parameters": {"effect": {"value": "[parameters('PostgreSQLFlexPublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-PostgreSql-Public-Network-Access", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b52376f7-9612-48a1-81cd-1ffe4b61032c", "parameters": {"effect": {"value": "[parameters('postgreSqlPublicNetworkAccess')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "MySQLFlexDenyPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c9299215-ae47-4f50-9c54-8a392f68a052", "parameters": {"effect": {"value": "[parameters('MySQLFlexPublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "BatchDenyPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/74c5a0ae-5e48-4738-b093-65e23a060488", "parameters": {"effect": {"value": "[parameters('BatchPublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "MariaDbDenyPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/fdccbe47-f3e3-4213-ad5d-ea459b2fa077", "parameters": {"effect": {"value": "[parameters('MariaDbPublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "MlDenyPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/438c38d2-3772-465a-a9cc-7a6666a275ce", "parameters": {"effect": {"value": "[parameters('MlPublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "RedisCacheDenyPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/470baccb-7e51-4549-8b1a-3e5be069f663", "parameters": {"effect": {"value": "[parameters('RedisCachePublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "BotServiceDenyPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/5e8168db-69e3-4beb-9822-57cb59202a9d", "parameters": {"effect": {"value": "[parameters('BotServicePublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "AutomationDenyPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/955a914f-bf86-4f0e-acd5-e0766b0efcb6", "parameters": {"effect": {"value": "[parameters('AutomationPublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "AppConfigDenyPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/3d9f5e4c-9947-4579-9539-2a7695fbc187", "parameters": {"effect": {"value": "[parameters('AppConfigPublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "FunctionDenyPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/969ac98b-88a8-449f-883c-2e9adb123127", "parameters": {"effect": {"value": "[parameters('FunctionPublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "FunctionAppSlotsDenyPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/11c82d0c-db9f-4d7b-97c5-f3f9aa957da2", "parameters": {"effect": {"value": "[parameters('FunctionAppSlotPublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "AseDenyPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/2d048aca-6479-4923-88f5-e2ac295d9af3", "parameters": {"effect": {"value": "[parameters('AsePublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "AsDenyPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1b5ef780-c53c-4a64-87f3-bb9c8c8094ba", "parameters": {"effect": {"value": "[parameters('AsPublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "ApiManDenyPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/df73bd95-24da-4a4f-96b9-4e8b94b402bd", "parameters": {"effect": {"value": "[parameters('ApiManPublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "ContainerAppsEnvironmentDenyPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/d074ddf8-01a5-4b5e-a2b8-964aed452c0a", "parameters": {"effect": {"value": "[parameters('ContainerAppsEnvironmentDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "<PERSON>y-Con<PERSON>er<PERSON>pps-Public-Network-Access", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/783ea2a8-b8fd-46be-896a-9ae79643a0b1", "parameters": {"effect": {"value": "[parameters('containerAppsPublicNetworkAccess')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "AsrVaultDenyPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/9ebbbba3-4d65-4da9-bb67-b22cfaaff090", "parameters": {"effect": {"value": "[parameters('AsrVaultDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-LogicApp-Public-Network-Access", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deny-LogicApp-Public-Network", "parameters": {"effect": {"value": "[parameters('logicAppPublicNetworkAccessEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-AppSlots-Public", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/701a595d-38fb-4a66-ae6d-fb3735217622", "parameters": {"effect": {"value": "[parameters('appSlotsPublicNetworkAccess')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-CognitiveSearch-PublicEndpoint", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ee980b6d-0eca-4501-8d54-f6290fd512c3", "parameters": {"effect": {"value": "[parameters('cognitiveSearchPublicNetworkAccess')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-ManagedDisk-Public-Network-Access", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/8405fdab-1faf-48aa-b702-999c9c172094", "parameters": {"effect": {"value": "[parameters('managedDiskPublicNetworkAccess')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-ADX-Public-Network-Access", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/43bc7be6-5e69-4b0d-a2bb-e815557ca673", "parameters": {"effect": {"value": "[parameters('adxPublicNetworkAccess')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Adf-Public-Network-Access", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1cf164be-6819-4a50-b8fa-4bcaa4f98fb6", "parameters": {"effect": {"value": "[parameters('adfPublicNetworkAccess')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-EventGrid-Public-Network-Access", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/f8f774be-6aee-492a-9e29-486ef81f3a68", "parameters": {"effect": {"value": "[parameters('eventGridPublicNetworkAccess')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-EventGrid-Topic-Public-Network-Access", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1adadefe-5f21-44f7-b931-a59b54ccdb45", "parameters": {"effect": {"value": "[parameters('eventGridTopicPublicNetworkAccess')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-EH-Public-Network-Access", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0602787f-9896-402a-a6e1-39ee63ee435e", "parameters": {"effect": {"value": "[parameters('eventHubNamespacesPublicNetworkAccess')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-KV-Hms-PublicNetwork", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/19ea9d63-adee-4431-a95e-1913c6c1c75f", "parameters": {"effect": {"value": "[parameters('keyVaultManagedHsmDisablePublicNetwork')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-MySql-Public-Network-Access", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/d9844e8a-1437-4aeb-a32c-0c992f056095", "parameters": {"effect": {"value": "[parameters('mySqlPublicNetworkAccess')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Cognitive-Services-Public-Network-Access", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0725b4dd-7e76-479c-a735-68e7ee23d5ca", "parameters": {"effect": {"value": "[parameters('cognitiveServicesPublicNetworkAccess')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Cognitive-Services-Network-Access", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/037eea7a-bd0a-46c5-9a66-03aea78705d3", "parameters": {"effect": {"value": "[parameters('cognitiveServicesNetworkAccess')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Sb-PublicEndpoint", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/cbd11fd3-3002-4907-b6c8-579f0e700e13", "parameters": {"effect": {"value": "[parameters('serviceBusDisablePublicNetworkAccess')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Sql-Managed-Public-Endpoint", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/9dfea752-dd46-4766-aed1-c355fa93fb91", "parameters": {"effect": {"value": "[parameters('sqlManagedPublicNetworkAccess')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Storage-Public-Access", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/4fa4b6c0-31ca-4c0d-b10d-24b96f62a751", "parameters": {"effect": {"value": "[parameters('storageAccountsPublicAccess')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Synapse-Public-Network-Access", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/38d8df46-cf4e-4073-8e03-48c24b29de0d", "parameters": {"effect": {"value": "[parameters('synapsePublicNetworkAccess')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Workspace-PublicNetworkAccess", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/87ac3038-c07a-4b92-860d-29e270a4f3cd", "parameters": {"effect": {"value": "[parameters('avdWorkspacePublicNetworkAccess')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "<PERSON><PERSON>-<PERSON><PERSON>-PublicNetworkAccess", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c25dcf31-878f-4eba-98eb-0818fdc6a334", "parameters": {"effect": {"value": "[parameters('avdHostPoolPublicNetworkAccess')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "<PERSON><PERSON><PERSON><PERSON><PERSON>-PublicNetworkAccess", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e8775d5a-73b7-4977-a39b-833ef0114628", "parameters": {"effect": {"value": "[parameters('grafanaPublicNetworkAccess')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}