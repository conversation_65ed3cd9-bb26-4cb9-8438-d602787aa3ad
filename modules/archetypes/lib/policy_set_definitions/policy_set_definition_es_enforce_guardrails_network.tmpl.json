{"name": "Enforce-Guardrails-Network", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Network and Networking services", "description": "This policy initiative is a group of policies that ensures Network and Networking services are compliant per regulated Landing Zones.", "metadata": {"version": "1.1.0", "category": "Network", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"subnetUdr": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "subnetNsg": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "subnetServiceEndpoint": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "appGwWaf": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "vnetModifyDdos": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Audit", "Modify", "Disabled"]}, "ddosPlanResourceId": {"type": "string", "defaultValue": ""}, "wafMode": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "wafModeRequirement": {"type": "string", "defaultValue": "Prevention"}, "wafFwRules": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "wafModeAppGw": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "wafModeAppGwRequirement": {"type": "string", "defaultValue": "Prevention"}, "denyMgmtFromInternet": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "denyMgmtFromInternetPorts": {"type": "Array", "metadata": {"displayName": "Ports", "description": "Ports to be blocked"}, "defaultValue": ["22", "3389"]}, "afwEnbaleTlsForAllAppRules": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "afwEnableTlsInspection": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "afwEmptyIDPSBypassList": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "afwEnableAllIDPSSignatureRules": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "afwEnableIDPS": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "wafAfdEnabled": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "vpnAzureAD": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "appGwTlsVersion": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "modifyUdr": {"type": "string", "defaultValue": "Disabled"}, "modifyUdrNextHopIpAddress": {"type": "string", "defaultValue": ""}, "modifyUdrNextHopType": {"type": "string", "defaultValue": "None"}, "modifyUdrAddressPrefix": {"type": "string", "defaultValue": "0.0.0.0/0"}, "modifyNsg": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Modify", "Disabled"]}, "modifyNsgRuleName": {"type": "string", "defaultValue": "DenyAnyInternetOutbound"}, "modifyNsgRulePriority": {"type": "integer", "defaultValue": 1000}, "modifyNsgRuleDirection": {"type": "string", "defaultValue": "Outbound"}, "modifyNsgRuleAccess": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Allow", "<PERSON><PERSON>"]}, "modifyNsgRuleProtocol": {"type": "string", "defaultValue": "*"}, "modifyNsgRuleSourceAddressPrefix": {"type": "string", "defaultValue": "*"}, "modifyNsgRuleSourcePortRange": {"type": "string", "defaultValue": "*"}, "modifyNsgRuleDestinationAddressPrefix": {"type": "string", "defaultValue": "Internet"}, "modifyNsgRuleDestinationPortRange": {"type": "string", "defaultValue": "*"}, "modifyNsgRuleDescription": {"type": "string", "defaultValue": "Deny any outbound traffic to the Internet"}}, "policyDefinitions": [{"policyDefinitionReferenceId": "Deny-Nsg-GW-subnet", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/35f9c03a-cc27-418e-9c0c-539ff999d010", "parameters": {}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-VPN-AzureAD", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/21a6bc25-125e-4d13-b82d-2e19b7208ab7", "parameters": {"effect": {"value": "[parameters('vpnAzureAD')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Waf-Afd-Enabled", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/055aa869-bc98-4af8-bafc-23f1ab6ffe2c", "parameters": {"effect": {"value": "[parameters('wafAfdEnabled')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Waf-IDPS", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6484db87-a62d-4327-9f07-80a2cbdf333a", "parameters": {"effect": {"value": "[parameters('afwEnableIDPS')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-FW-AllIDPSS", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/610b6183-5f00-4d68-86d2-4ab4cb3a67a5", "parameters": {"effect": {"value": "[parameters('afwEnableAllIDPSSignatureRules')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-FW-EmpIDPSBypass", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/f516dc7a-4543-4d40-aad6-98f76a706b50", "parameters": {"effect": {"value": "[parameters('afwEmptyIDPSBypassList')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-FW-TLS-Inspection", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/711c24bb-7f18-4578-b192-81a6161e1f17", "parameters": {"effect": {"value": "[parameters('afwEnableTlsInspection')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-FW-TLS-AllApp", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a58ac66d-92cb-409c-94b8-8e48d7a96596", "parameters": {"effect": {"value": "[parameters('afwEnbaleTlsForAllAppRules')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Waf-AppGw-mode", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/12430be1-6cc8-4527-a9a8-e3d38f250096", "parameters": {"effect": {"value": "[parameters('wafModeAppGw')]"}, "modeRequirement": {"value": "[parameters('wafModeAppGwRequirement')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Waf-Fw-rules", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/632d3993-e2c0-44ea-a7db-2eca131f356d", "parameters": {"effect": {"value": "[parameters('wafFwRules')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Waf-mode", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/425bea59-a659-4cbb-8d31-34499bd030b8", "parameters": {"effect": {"value": "[parameters('wafMode')]"}, "modeRequirement": {"value": "[parameters('wafModeRequirement')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Modify-vNet-DDoS", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/94de2ad3-e0c1-4caf-ad78-5d47bbc83d3d", "parameters": {"effect": {"value": "[parameters('vnetModifyDdos')]"}, "ddosPlan": {"value": "[parameters('ddosPlanResourceId')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Ip-Forwarding", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/88c0b9da-ce96-4b03-9635-f29a937e2900", "parameters": {}, "groupNames": []}, {"policyDefinitionReferenceId": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/83a86a26-fd1f-447c-b59d-e51f44264114", "parameters": {}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-AppGw-Without-Waf", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/564feb30-bf6a-4854-b4bb-0d2d2d1e6c66", "parameters": {"effect": {"value": "[parameters('appGwWaf')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Subnet-Without-Udr", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deny-Subnet-Without-Udr", "parameters": {"effect": {"value": "[parameters('subnetUdr')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Subnet-Without-NSG", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deny-Subnet-Without-Nsg", "parameters": {"effect": {"value": "[parameters('subnetNsg')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Subnet-with-Service-Endpoints", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deny-Service-Endpoints", "parameters": {"effect": {"value": "[parameters('subnetServiceEndpoint')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Mgmt-From-Internet", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deny-MgmtPorts-From-Internet", "parameters": {"effect": {"value": "[parameters('denyMgmtFromInternet')]"}, "ports": {"value": "[parameters('denyMgmtFromInternetPorts')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-AppGw-Without-Tls", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deny-AppGw-Without-Tls", "parameters": {"effect": {"value": "[parameters('appGwTlsVersion')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Modify-Udr", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Modify-UDR", "parameters": {"effect": {"value": "[parameters('modifyUdr')]"}, "nextHopIpAddress": {"value": "[parameters('modifyUdrNextHopIpAddress')]"}, "nextHopType": {"value": "[parameters('modifyUdrNextHopType')]"}, "addressPrefix": {"value": "[parameters('modifyUdrAddressPrefix')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Modify-Nsg", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Modify-NSG", "parameters": {"effect": {"value": "[parameters('modifyNsg')]"}, "nsgRuleName": {"value": "[parameters('modifyNsgRuleName')]"}, "nsgRulePriority": {"value": "[parameters('modifyNsgRulePriority')]"}, "nsgRuleDirection": {"value": "[parameters('modifyNsgRuleDirection')]"}, "nsgRuleAccess": {"value": "[parameters('modifyNsgRuleAccess')]"}, "nsgRuleProtocol": {"value": "[parameters('modifyNsgRuleProtocol')]"}, "nsgRuleSourceAddressPrefix": {"value": "[parameters('modifyNsgRuleSourceAddressPrefix')]"}, "nsgRuleSourcePortRange": {"value": "[parameters('modifyNsgRuleSourcePortRange')]"}, "nsgRuleDestinationAddressPrefix": {"value": "[parameters('modifyNsgRuleDestinationAddressPrefix')]"}, "nsgRuleDestinationPortRange": {"value": "[parameters('modifyNsgRuleDestinationPortRange')]"}, "nsgRuleDescription": {"value": "[parameters('modifyNsgRuleDescription')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}