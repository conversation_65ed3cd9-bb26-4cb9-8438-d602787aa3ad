{"name": "DenyAction-DeleteProtection", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "DenyAction Delete - Activity Log Settings and Diagnostic Settings", "description": "Enforces DenyAction - Delete on Activity Log Settings and Diagnostic Settings.", "metadata": {"version": "1.0.0", "category": "Monitoring", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {}, "policyDefinitions": [{"policyDefinitionReferenceId": "DenyActionDelete-DiagnosticSettings", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/DenyAction-DiagnosticLogs", "parameters": {}, "groupNames": []}, {"policyDefinitionReferenceId": "DenyActionDelete-ActivityLogSettings", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/DenyAction-ActivityLogs", "parameters": {}, "groupNames": []}], "policyDefinitionGroups": null}}