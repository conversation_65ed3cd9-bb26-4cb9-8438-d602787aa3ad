{"name": "Enforce-Guardrails-EventHub", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Event Hub", "description": "This policy initiative is a group of policies that ensures Event Hub is compliant per regulated Landing Zones.", "metadata": {"version": "1.0.0", "category": "Event Hub", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"eventHubAuthRules": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "eventHubNamespacesLocalAuth": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "eventHubNamespacesModifyLocalAuth": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "eventHubNamespacesDoubleEncryption": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionReferenceId": "Deny-EH-Double-Encryption", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/836cd60e-87f3-4e6a-a27c-29d687f01a4c", "parameters": {"effect": {"value": "[parameters('eventHubNamespacesDoubleEncryption')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Modify-EH-Local-Auth", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/57f35901-8389-40bb-ac49-3ba4f86d889d", "parameters": {"effect": {"value": "[parameters('eventHubNamespacesModifyLocalAuth')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-EH-Local-Auth", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/5d4e3c65-4873-47be-94f3-6f8b953a3598", "parameters": {"effect": {"value": "[parameters('eventHubNamespacesLocalAuth')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "<PERSON><PERSON><PERSON><PERSON><PERSON>-Auth-Rules", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b278e460-7cfc-4451-8294-cccc40a940d7", "parameters": {"effect": {"value": "[parameters('eventHubAuthRules')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}