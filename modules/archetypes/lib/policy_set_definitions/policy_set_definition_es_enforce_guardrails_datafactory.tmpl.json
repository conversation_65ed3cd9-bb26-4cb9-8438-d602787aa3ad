{"name": "Enforce-Guardrails-DataFactory", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Data Factory", "description": "This policy initiative is a group of policies that ensures Data Factory is compliant per regulated Landing Zones.", "metadata": {"version": "1.0.0", "category": "Data Factory", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"adfSqlIntegration": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "adfLinkedServiceKeyVault": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "adfGit": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "adfManagedIdentity": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "adfModifyPublicNetworkAccess": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionReferenceId": "<PERSON><PERSON>-<PERSON><PERSON>-Managed-Identity", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/f78ccdb4-7bf4-4106-8647-270491d2978a", "parameters": {"effect": {"value": "[parameters('adfManagedIdentity')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "<PERSON>y-<PERSON><PERSON><PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/77d40665-3120-4348-b539-3192ec808307", "parameters": {"effect": {"value": "[parameters('adfGit')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Adf-Linked-Service-Key-Vault", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/127ef6d7-242f-43b3-9eef-947faf1725d0", "parameters": {"effect": {"value": "[parameters('adfLinkedServiceKeyVault')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Adf-Sql-Integration", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0088bc63-6dee-4a9c-9d29-91cfdc848952", "parameters": {"effect": {"value": "[parameters('adfSqlIntegration')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Modify-Adf-Public-Network-Access", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/08b1442b-7789-4130-8506-4f99a97226a7", "parameters": {"effect": {"value": "[parameters('adfModifyPublicNetworkAccess')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}