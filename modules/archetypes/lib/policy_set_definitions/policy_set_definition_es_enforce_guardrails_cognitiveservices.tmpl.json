{"name": "Enforce-Guardrails-CognitiveServices", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Cognitive Services", "description": "This policy initiative is a group of policies that ensures Cognitive Services is compliant per regulated Landing Zones.", "metadata": {"version": "1.1.0", "category": "Cognitive Services", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"cognitiveSearchSku": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "cognitiveSearchLocalAuth": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "modifyCognitiveSearchLocalAuth": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "cognitiveServicesLocalAuth": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "modifyCognitiveSearchPublicEndpoint": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "cognitiveServicesModifyPublicNetworkAccess": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "cognitiveServicesManagedIdentity": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "cognitiveServicesCustomerStorage": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "cognitiveServicesResourceLogs": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionReferenceId": "Deny-CognitiveSearch-SKU", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a049bf77-880b-470f-ba6d-9f21c530cf83", "parameters": {"effect": {"value": "[parameters('cognitiveSearchSku')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-CongitiveSearch-LocalAuth", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6300012e-e9a4-4649-b41f-a85f5c43be91", "parameters": {"effect": {"value": "[parameters('cognitiveSearchLocalAuth')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Modify-CogntiveSearch-LocalAuth", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/4eb216f2-9dba-4979-86e6-5d7e63ce3b75", "parameters": {"effect": {"value": "[parameters('modifyCognitiveSearchLocalAuth')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Modify-CogntiveSearch-PublicEndpoint", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/9cee519f-d9c1-4fd9-9f79-24ec3449ed30", "parameters": {"effect": {"value": "[parameters('modifyCognitiveSearchPublicEndpoint')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Modify-Cognitive-Services-Public-Network-Access", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/47ba1dd7-28d9-4b07-a8d5-9813bed64e0c", "parameters": {"effect": {"value": "[parameters('cognitiveServicesModifyPublicNetworkAccess')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Cognitive-Services-Managed-Identity", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/fe3fd216-4f83-4fc1-8984-2bbec80a3418", "parameters": {"effect": {"value": "[parameters('cognitiveServicesManagedIdentity')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Cognitive-Services-Customer-Storage", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/46aa9b05-0e60-4eae-a88b-1e9d374fa515", "parameters": {"effect": {"value": "[parameters('cognitiveServicesCustomerStorage')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Modify-Cognitive-Services-Local-Auth", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/14de9e63-1b31-492e-a5a3-c3f7fd57f555", "parameters": {"effect": {"value": "[parameters('cognitiveServicesLocalAuth')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Aine-Cognitive-Services-Resource-Logs", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b4330a05-a843-4bc8-bf9a-cacce50c67f4", "parameters": {"effect": {"value": "[parameters('cognitiveServicesResourceLogs')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}