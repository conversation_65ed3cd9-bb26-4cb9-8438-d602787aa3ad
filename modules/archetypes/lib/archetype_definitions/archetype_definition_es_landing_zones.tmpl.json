{"es_landing_zones": {"policy_assignments": ["Audit-AppGW-WAF", "Deny-IP-forwarding", "Deny-MgmtPorts-Internet", "Deny-Priv-Esc-AKS", "Deny-Privileged-AKS", "Deny-Storage-http", "Deny-Subnet-Without-Nsg", "Deploy-AzSqlDb-Auditing", "Deploy-MDFC-DefSQL-AMA", "Deploy-SQL-TDE", "Deploy-SQL-Threat", "Deploy-VM-Backup", "Deploy-VM-ChangeTrack", "Deploy-VM-Monitoring", "Deploy-vmArc-ChangeTrack", "Deploy-vmHybr-Monitoring", "Deploy-VMSS-ChangeTrack", "Deploy-VMSS-Monitoring", "Enable-AUM-CheckUpdates", "Enable-DDoS-VNET", "Enforce-AKS-HTTPS", "Enforce-ASR", "Enforce-GR-Key<PERSON>ault", "Enforce-Subnet-Private", "Enforce-TLS-SSL-H224"], "policy_definitions": [], "policy_set_definitions": [], "role_definitions": [], "archetype_config": {"parameters": {}, "access_control": {}}}}