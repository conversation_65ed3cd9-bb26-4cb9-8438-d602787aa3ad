{"es_root": {"policy_assignments": ["Audit-ResourceRGLocation", "Audit-TrustedLaunch", "Audit-UnusedResources", "Audit-ZoneResiliency", "Deny-Classic-Resources", "Deny-UnmanagedDisk", "Deploy-ASC-Monitoring", "Deploy-AzActivity-Log", "Deploy-Diag-LogsCat", "Deploy-MDEndpoints", "Deploy-MDEndpointsAMA", "Deploy-MDFC-Config-H224", "Deploy-MDFC-OssDb", "Deploy-MDFC-SqlAtp", "Enforce-ACSB"], "policy_definitions": ["Append-AppService-httpsonly", "Append-AppService-latestTLS", "Append-KV-SoftDelete", "Append-Redis-disableNonSslPort", "Append-Redis-sslEnforcement", "Audit-AzureHybridBenefit", "Audit-Disks-UnusedResourcesCostOptimization", "Audit-MachineLearning-PrivateEndpointId", "Audit-PrivateLinkDnsZones", "Audit-PublicIpAddresses-UnusedResourcesCostOptimization", "Audit-ServerFarms-UnusedResourcesCostOptimization", "Deny-AA-child-resources", "Deny-APIM-TLS", "Deny-AppGw-Without-Tls", "Deny-AppGW-Without-WAF", "Deny-AppService-without-BYOC", "Deny-AppServiceApiApp-http", "Deny-AppServiceFunctionApp-http", "Deny-AppServiceWebApp-http", "Deny-AzFw-Without-Policy", "Deny-CognitiveServices-NetworkAcls", "Deny-CognitiveServices-Resource-Kinds", "Deny-CognitiveServices-RestrictOutboundNetworkAccess", "Deny-Databricks-NoPublicIp", "Deny-Databricks-Sku", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-VirtualNetwork", "Deny-EH-minTLS", "Deny-EH-Premium-CMK", "Deny-FileServices-InsecureAuth", "Deny-FileServices-InsecureKerberos", "Deny-FileServices-InsecureSmbChannel", "Deny-FileServices-InsecureSmbVersions", "<PERSON>y-LogicApp-Public-Network", "Deny-LogicApps-Without-Https", "Deny-MachineLearning-Aks", "Deny-MachineLearning-Compute-SubnetId", "Deny-MachineLearning-Compute-VmSize", "Deny-MachineLearning-ComputeCluster-RemoteLoginPortPublicAccess", "Deny-MachineLearning-ComputeCluster-Scale", "Deny-MachineLearning-HbiWorkspace", "Deny-MachineLearning-PublicAccessWhenBehindVnet", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-PublicNetworkAccess", "Deny-MgmtPorts-From-Internet", "Deny-MySql-http", "Deny-PostgreSql-http", "Deny-Private-DNS-Zones", "Deny-PublicEndpoint-MariaDB", "Deny-PublicIP", "Deny-RDP-From-Internet", "Deny-Redis-http", "Deny-Service-Endpoints", "Deny-Sql-minTLS", "Deny-SqlMi-minTLS", "Deny-Storage-ContainerDeleteRetentionPolicy", "Deny-Storage-CopyScope", "Deny-Storage-CorsRules", "Deny-Storage-LocalUser", "Deny-Storage-minTLS", "Deny-Storage-NetworkAclsBypass", "Deny-Storage-NetworkAclsVirtualNetworkRules", "Deny-Storage-ResourceAccessRulesResourceId", "Deny-Storage-ResourceAccessRulesTenantId", "Deny-Storage-ServicesEncryption", "Deny-Storage-SFTP", "Deny-StorageAccount-CustomDomain", "Deny-Subnet-Without-Nsg", "<PERSON>y-Subnet-Without-Pen<PERSON>", "Deny-Subnet-Without-Udr", "Deny-UDR-With-Specific-NextHop", "Deny-VNET-Peer-Cross-Sub", "Deny-VNET-Peering-To-Non-Approved-VNETs", "Deny-VNet-Peering", "DenyAction-ActivityLogs", "DenyAction-DeleteResources", "DenyAction-DiagnosticLogs", "Deploy-ASC-SecurityContacts", "Deploy-Budget", "Deploy-Custom-Route-Table", "Deploy-DDoSProtection", "Deploy-Diagnostics-AA", "Deploy-Diagnostics-ACI", "Deploy-Diagnostics-ACR", "Deploy-Diagnostics-AnalysisService", "Deploy-Diagnostics-ApiForFHIR", "Deploy-Diagnostics-APIMgmt", "Deploy-Diagnostics-ApplicationGateway", "Deploy-Diagnostics-AVDScalingPlans", "Deploy-Diagnostics-Bastion", "Deploy-Diagnostics-CDNEndpoints", "Deploy-Diagnostics-CognitiveServices", "Deploy-Diagnostics-CosmosDB", "Deploy-Diagnostics-Databricks", "Deploy-Diagnostics-DataExplorerCluster", "Deploy-Diagnostics-DataFactory", "Deploy-Diagnostics-DLAnalytics", "Deploy-Diagnostics-EventGridSub", "Deploy-Diagnostics-EventGridSystemTopic", "Deploy-Diagnostics-EventGridTopic", "Deploy-Diagnostics-ExpressRoute", "Deploy-Diagnostics-Firewall", "Deploy-Diagnostics-FrontDoor", "Deploy-Diagnostics-Function", "Deploy-Diagnostics-HDInsight", "Deploy-Diagnostics-iotHub", "Deploy-Diagnostics-LoadBalancer", "Deploy-Diagnostics-LogAnalytics", "Deploy-Diagnostics-LogicAppsISE", "Deploy-Diagnostics-MariaDB", "Deploy-Diagnostics-MediaService", "Deploy-Diagnostics-MlWorkspace", "Deploy-Diagnostics-MySQL", "Deploy-Diagnostics-NetworkSecurityGroups", "Deploy-Diagnostics-NIC", "Deploy-Diagnostics-PostgreSQL", "Deploy-Diagnostics-PowerBIEmbedded", "Deploy-Diagnostics-RedisCache", "Deploy-Diagnostics-Relay", "Deploy-Diagnostics-SignalR", "Deploy-Diagnostics-SQLElasticPools", "Deploy-Diagnostics-SQLMI", "Deploy-Diagnostics-TimeSeriesInsights", "Deploy-Diagnostics-TrafficManager", "Deploy-Diagnostics-VirtualNetwork", "Deploy-Diagnostics-VM", "Deploy-Diagnostics-VMSS", "Deploy-Diagnostics-VNetGW", "Deploy-Diagnostics-VWanS2SVPNGW", "Deploy-Diagnostics-WebServerFarm", "Deploy-Diagnostics-Website", "Deploy-Diagnostics-WVDAppGroup", "Deploy-Diagnostics-WVDHostPools", "Deploy-Diagnostics-WVDWorkspace", "Deploy-FirewallPolicy", "Deploy-LogicApp-TLS", "Deploy-MDFC-Arc-SQL-DCR-Association", "Deploy-MDFC-Arc-Sql-DefenderSQL-DCR", "Deploy-MDFC-SQL-AMA", "Deploy-MDFC-SQL-DefenderSQL-DCR", "Deploy-MDFC-SQL-DefenderSQL", "Deploy-MySQL-sslEnforcement", "Deploy-Nsg-FlowLogs-to-LA", "Deploy-Nsg-FlowLogs", "Deploy-PostgreSQL-sslEnforcement", "Deploy-Private-DNS-Generic", "Deploy-Sql-AuditingSettings", "Deploy-SQL-minTLS", "Deploy-Sql-SecurityAlertPolicies", "Deploy-Sql-Tde", "Deploy-Sql-vulnerabilityAssessments_20230706", "Deploy-Sql-vulnerabilityAssessments", "Deploy-SqlMi-minTLS", "Deploy-Storage-sslEnforcement", "Deploy-UserAssignedManagedIdentity-VMInsights", "Deploy-Vm-autoShutdown", "Deploy-VNET-HubSpoke", "Deploy-Windows-DomainJoin", "Modify-NSG", "Modify-UDR"], "policy_set_definitions": ["Audit-TrustedLaunch", "Audit-UnusedResourcesCostOptimization", "Deny-PublicPaaSEndpoints", "DenyAction-DeleteProtection", "Deploy-AUM-CheckUpdates", "Deploy-Diagnostics-LogAnalytics", "Deploy-MDFC-Config_20240319", "Deploy-MDFC-Config", "Deploy-MDFC-DefenderSQL-AMA", "Deploy-Private-DNS-Zones", "Deploy-Sql-Security_20240529", "Deploy-Sql-Security", "Enforce-ACSB", "Enforce-ALZ-Decomm", "Enforce-ALZ-Sandbox", "Enforce-Backup", "Enforce-Encryption-CMK", "Enforce-EncryptTransit_20240509", "Enforce-EncryptTransit", "Enforce-Guardrails-APIM", "Enforce-Guardrails-AppServices", "Enforce-Guardrails-Automation", "Enforce-Guardrails-BotService", "Enforce-Guardrails-CognitiveServices", "Enforce-Guardrails-Compute", "Enforce-Guardrails-ContainerApps", "Enforce-Guardrails-ContainerInstance", "Enforce-Guardrails-ContainerRegistry", "Enforce-Guardrails-CosmosDb", "Enforce-Guardrails-DataExplorer", "Enforce-Guardrails-DataFactory", "Enforce-Guardrails-EventGrid", "Enforce-Guardrails-EventHub", "Enforce-Guardrails-KeyVault-Sup", "Enforce-Guardrails-KeyVault", "Enforce-Guardrails-Kubernetes", "Enforce-Guardrails-MachineLearning", "Enforce-Guardrails-MySQL", "Enforce-Guardrails-Network", "Enforce-Guardrails-OpenAI", "Enforce-Guardrails-PostgreSQL", "Enforce-Guardrails-ServiceBus", "Enforce-Guardrails-SQL", "Enforce-Guardrails-Storage", "Enforce-Guardrails-Synapse", "Enforce-Guardrails-VirtualDesktop"], "role_definitions": ["Network-Subnet-Contributor", "Application-Owners", "Network-Management", "Security-Operations", "Subscription-Owner"], "archetype_config": {"parameters": {}, "access_control": {}}}}