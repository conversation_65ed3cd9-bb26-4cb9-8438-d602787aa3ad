# Log Analytics Module Variables

variable "resource_prefix" {
  type        = string
  description = "Prefix for resource naming"
}

variable "resource_group_name" {
  type        = string
  description = "Name of the resource group"
}

variable "location" {
  type        = string
  description = "Azure region for resources"
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to resources"
  default     = {}
}

# Log Analytics Configuration
variable "sku" {
  type        = string
  description = "SKU for Log Analytics workspace"
  default     = "PerGB2018"
  
  validation {
    condition     = contains(["Free", "Standalone", "PerNode", "PerGB2018"], var.sku)
    error_message = "SKU must be one of: Free, Standalone, PerNode, PerGB2018."
  }
}

variable "retention_in_days" {
  type        = number
  description = "Data retention in days for Log Analytics workspace"
  default     = 30
  
  validation {
    condition     = var.retention_in_days >= 30 && var.retention_in_days <= 730
    error_message = "Retention must be between 30 and 730 days."
  }
}

variable "daily_quota_gb" {
  type        = number
  description = "Daily ingestion quota in GB (-1 for unlimited)"
  default     = -1
}

# Monitoring Configuration
variable "enable_monitoring_for_vm" {
  type        = bool
  description = "Enable monitoring for virtual machines"
  default     = true
}

variable "enable_monitoring_for_vmss" {
  type        = bool
  description = "Enable monitoring for virtual machine scale sets"
  default     = true
}

# Solutions Configuration
variable "enable_sentinel" {
  type        = bool
  description = "Enable Microsoft Sentinel"
  default     = true
}

variable "enable_change_tracking" {
  type        = bool
  description = "Enable Change Tracking solution"
  default     = true
}

variable "enable_updates" {
  type        = bool
  description = "Enable Updates solution"
  default     = true
}

variable "enable_security" {
  type        = bool
  description = "Enable Security solution"
  default     = true
}

variable "enable_agent_health_assessment" {
  type        = bool
  description = "Enable Agent Health Assessment solution"
  default     = true
}

variable "enable_anti_malware" {
  type        = bool
  description = "Enable Anti-Malware solution"
  default     = true
}

variable "enable_container_insights" {
  type        = bool
  description = "Enable Container Insights solution"
  default     = true
}
