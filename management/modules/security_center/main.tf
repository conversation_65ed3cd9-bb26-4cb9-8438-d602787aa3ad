# Security Center Module
# Provides Microsoft Defender for Cloud configuration

terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.107"
    }
  }
}

# Get current subscription
data "azurerm_client_config" "current" {}

# Security Center Contact
resource "azurerm_security_center_contact" "main" {
  count = var.email_security_contact != null ? 1 : 0
  
  email               = var.email_security_contact
  phone               = var.phone_security_contact
  alert_notifications = true
  alerts_to_admins    = true
}

# Security Center Auto Provisioning - DEPRECATED
# resource "azurerm_security_center_auto_provisioning" "main" {
#   auto_provision = "On"
# }

# Security Center Settings - Already exist, commenting out to avoid conflicts
# resource "azurerm_security_center_setting" "mcas" {
#   setting_name = "MCAS"
#   enabled      = true
# }

# resource "azurerm_security_center_setting" "wdatp" {
#   setting_name = "WDATP"
#   enabled      = true
# }

# Microsoft Defender for APIs
resource "azurerm_security_center_subscription_pricing" "apis" {
  count         = var.enable_defender_for_apis ? 1 : 0
  tier          = "Standard"
  resource_type = "Api"
  subplan       = "P1"  # Required for APIs - P1, P2, P3, P4, P5 available
}

# Microsoft Defender for App Services
resource "azurerm_security_center_subscription_pricing" "app_services" {
  count         = var.enable_defender_for_app_services ? 1 : 0
  tier          = "Standard"
  resource_type = "AppServices"
}

# Microsoft Defender for ARM
resource "azurerm_security_center_subscription_pricing" "arm" {
  count         = var.enable_defender_for_arm ? 1 : 0
  tier          = "Standard"
  resource_type = "Arm"
}

# Microsoft Defender for Containers
resource "azurerm_security_center_subscription_pricing" "containers" {
  count         = var.enable_defender_for_containers ? 1 : 0
  tier          = "Standard"
  resource_type = "Containers"
}

# Microsoft Defender for Cosmos DB
resource "azurerm_security_center_subscription_pricing" "cosmosdbs" {
  count         = var.enable_defender_for_cosmosdbs ? 1 : 0
  tier          = "Standard"
  resource_type = "CosmosDbs"
}

# Microsoft Defender for CSPM
resource "azurerm_security_center_subscription_pricing" "cspm" {
  count         = var.enable_defender_for_cspm ? 1 : 0
  tier          = "Standard"
  resource_type = "CloudPosture"
}

# Microsoft Defender for DNS
resource "azurerm_security_center_subscription_pricing" "dns" {
  count         = var.enable_defender_for_dns ? 1 : 0
  tier          = "Standard"
  resource_type = "Dns"
}

# Microsoft Defender for Key Vault
resource "azurerm_security_center_subscription_pricing" "key_vault" {
  count         = var.enable_defender_for_key_vault ? 1 : 0
  tier          = "Standard"
  resource_type = "KeyVaults"
}

# Microsoft Defender for Open Source Databases
resource "azurerm_security_center_subscription_pricing" "oss_databases" {
  count         = var.enable_defender_for_oss_databases ? 1 : 0
  tier          = "Standard"
  resource_type = "OpenSourceRelationalDatabases"
}

# Microsoft Defender for Servers
resource "azurerm_security_center_subscription_pricing" "servers" {
  count         = var.enable_defender_for_servers ? 1 : 0
  tier          = "Standard"
  resource_type = "VirtualMachines"

  dynamic "extension" {
    for_each = var.enable_defender_for_servers_vulnerability_assessments ? [1] : []
    content {
      name = "MdeDesignatedSubscription"
    }
  }
}

# Microsoft Defender for SQL Servers
resource "azurerm_security_center_subscription_pricing" "sql_servers" {
  count         = var.enable_defender_for_sql_servers ? 1 : 0
  tier          = "Standard"
  resource_type = "SqlServers"
}

# Microsoft Defender for SQL Server VMs
resource "azurerm_security_center_subscription_pricing" "sql_server_vms" {
  count         = var.enable_defender_for_sql_server_vms ? 1 : 0
  tier          = "Standard"
  resource_type = "SqlServerVirtualMachines"
}

# Microsoft Defender for Storage
resource "azurerm_security_center_subscription_pricing" "storage" {
  count         = var.enable_defender_for_storage ? 1 : 0
  tier          = "Standard"
  resource_type = "StorageAccounts"

  dynamic "extension" {
    for_each = var.enable_defender_for_storage_malware_scanning ? [1] : []
    content {
      name = "OnUploadMalwareScanning"
      additional_extension_properties = {
        CapGBPerMonthPerStorageAccount = var.defender_for_storage_malware_scanning_cap_gb_per_month
      }
    }
  }

  dynamic "extension" {
    for_each = var.enable_defender_for_storage_sensitive_data_discovery ? [1] : []
    content {
      name = "SensitiveDataDiscovery"
    }
  }
}

# Log Analytics Workspace Integration
# Note: This will be created after the workspace is available
# resource "azurerm_security_center_workspace" "main" {
#   count        = var.log_analytics_workspace_id != null ? 1 : 0
#   scope        = "/subscriptions/${data.azurerm_client_config.current.subscription_id}"
#   workspace_id = var.log_analytics_workspace_id
# }
