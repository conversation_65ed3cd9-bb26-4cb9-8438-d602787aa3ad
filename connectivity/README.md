# Connectivity Module

## Overview

The Connectivity module provides networking infrastructure for the EWH Landing Zone. It creates a hub-and-spoke network architecture with essential networking components including virtual networks, subnets, network security groups, and DDoS protection.

## Features

- **Hub Virtual Network**: Central networking hub with dedicated subnets
- **Gateway Subnet**: For VPN Gateway or ExpressRoute Gateway
- **Firewall Subnet**: For Azure Firewall deployment
- **Management Subnet**: For management and monitoring resources
- **Network Security Groups**: Pre-configured security rules
- **DDoS Protection**: Optional DDoS protection plan
- **Resource Group**: Dedicated resource group for networking resources

## Architecture

```
Hub Virtual Network (10.0.0.0/16)
├── Gateway Subnet (********/27)
├── Firewall Subnet (********/26)
└── Management Subnet (********/24)
```

## Dependencies

- Core module (for management group structure)
- Management module (for monitoring and security)

## Usage

```hcl
module "connectivity" {
  source = "./connectivity"

  root_id                        = "ewh"
  root_name                      = "EWH"
  primary_location               = "southeastasia"
  secondary_location             = "eastasia"
  subscription_id_connectivity   = "subscription-id"
  enable_ddos_protection         = true
  connectivity_resources_tags    = {
    Purpose = "Networking"
  }
  tags = {
    Environment = "Production"
    Project     = "EWH Landing Zone"
  }
}
```

## Variables

| Variable | Type | Description | Default |
|----------|------|-------------|---------|
| `root_id` | string | Root management group ID | - |
| `root_name` | string | Root management group name | "EWH" |
| `primary_location` | string | Primary Azure region | "southeastasia" |
| `secondary_location` | string | Secondary Azure region | "eastasia" |
| `subscription_id_connectivity` | string | Subscription ID for connectivity resources | null |
| `enable_ddos_protection` | bool | Enable DDoS protection | true |
| `connectivity_resources_tags` | map(string) | Additional tags for connectivity resources | {} |
| `tags` | map(string) | Default tags for all resources | {} |

## Outputs

| Output | Description |
|--------|-------------|
| `resource_group_name` | Name of the connectivity resource group |
| `hub_vnet_id` | ID of the hub virtual network |
| `gateway_subnet_id` | ID of the gateway subnet |
| `firewall_subnet_id` | ID of the firewall subnet |
| `management_subnet_id` | ID of the management subnet |
| `hub_nsg_id` | ID of the hub network security group |
| `ddos_protection_plan_id` | ID of the DDoS protection plan |
| `connectivity_resources` | Summary of all connectivity resources |

## Security

The module includes pre-configured network security groups with:
- HTTPS (443) access allowed
- SSH (22) access restricted to internal network (10.0.0.0/16)

## Best Practices

1. **Subnet Sizing**: Subnets are sized according to Azure best practices
2. **Security**: NSGs are applied to all subnets
3. **Naming Convention**: Resources follow consistent naming patterns
4. **Tagging**: Comprehensive tagging for resource management
5. **Dependencies**: Proper dependency management between modules

## Future Enhancements

- Spoke virtual networks for workload isolation
- Azure Firewall deployment
- VPN Gateway configuration
- ExpressRoute Gateway support
- Network peering between regions
