# Configure Terraform to set the required AzureRM provider
# version and features{} block

terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.107"
    }
  }
}

# Get the current client configuration from the AzureRM provider
data "azurerm_client_config" "current" {}

# Azure Landing Zone Connectivity Module
module "alz_connectivity" {
  source  = "Azure/caf-enterprise-scale/azurerm"
  version = "~> 6.2.0"

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm
    azurerm.management   = azurerm
  }

  # Base module configuration settings
  root_parent_id = data.azurerm_client_config.current.tenant_id
  root_id        = var.root_id
  default_location = var.primary_location

  # Disable creation of the core management group hierarchy
  # as we only want connectivity resources
  deploy_core_landing_zones = false
  deploy_management_resources = false

  # Configuration settings for connectivity resources
  # All resource (subnet, virtual network gw, firewall, dns, bastion, ddos) defined in settings.connectivity.tf
  deploy_connectivity_resources    = true
  configure_connectivity_resources = local.configure_connectivity_resources
  subscription_id_connectivity     = var.subscription_id_connectivity
}


# ==============================================================================
# MANAGEMENT NETWORKING
# ==============================================================================

# Network Security Group for Shared Services Subnet
resource "azurerm_network_security_group" "shared_services_nsg" {
  depends_on = [module.alz_connectivity]

  name                = "${var.root_id}-nsg-shared-services"
  location            = var.primary_location
  resource_group_name = "${var.root_id}-connectivity-${var.primary_location}"

  security_rule {
    name                       = "Allow_RDP_Inbound"
    priority                   = 1000
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "3389"
    source_address_prefix      = "**********/16"
    destination_address_prefix = "*"
  }

  security_rule {
    name                       = "Allow_SSH_Inbound"
    priority                   = 1001
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "22"
    source_address_prefix      = "**********/16"
    destination_address_prefix = "*"
  }

  security_rule {
    name                       = "Allow_HTTP_Inbound"
    priority                   = 1002
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "80"
    source_address_prefix      = "*"
    destination_address_prefix = "*"
  }

  security_rule {
    name                       = "Allow_HTTPS_Inbound"
    priority                   = 1003
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "443"
    source_address_prefix      = "*"
    destination_address_prefix = "*"
  }

  security_rule {
    name                       = "Deny_All_Inbound"
    priority                   = 4096
    direction                  = "Inbound"
    access                     = "Deny"
    protocol                   = "*"
    source_port_range          = "*"
    destination_port_range     = "*"
    source_address_prefix      = "*"
    destination_address_prefix = "*"
  }

  tags = var.connectivity_resources_tags
}

# Use existing management resource group from management module
data "azurerm_resource_group" "management_network" {
  name = "${var.root_id}-rg-management-${var.primary_location}"

  depends_on = [
    # Ensure management module creates the resource group first
  ]
}

# Management Virtual Network
resource "azurerm_virtual_network" "management_vnet" {
  name                = "${var.root_id}-vnet-management-${var.primary_location}"
  location            = data.azurerm_resource_group.management_network.location
  resource_group_name = data.azurerm_resource_group.management_network.name
  address_space       = local.management_network_config.address_space
  tags                = var.connectivity_resources_tags
}

# Management Subnets - Dynamic creation based on configuration
resource "azurerm_subnet" "management_subnets" {
  for_each = local.management_network_config.management_subnets

  name                 = "${var.root_id}-subnet-${each.value.name}"
  resource_group_name  = data.azurerm_resource_group.management_network.name
  virtual_network_name = azurerm_virtual_network.management_vnet.name
  address_prefixes     = each.value.address_prefixes
}

# Network Security Group for Management Shared Resource Subnet
resource "azurerm_network_security_group" "management_shared_resource_nsg" {
  name                = "${var.root_id}-nsg-management-shared-resource"
  location            = data.azurerm_resource_group.management_network.location
  resource_group_name = data.azurerm_resource_group.management_network.name

  security_rule {
    name                       = "Allow_RDP_Inbound"
    priority                   = 1000
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "3389"
    source_address_prefix      = "**********/16"
    destination_address_prefix = "*"
  }

  security_rule {
    name                       = "Allow_SSH_Inbound"
    priority                   = 1001
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "22"
    source_address_prefix      = "**********/16"
    destination_address_prefix = "*"
  }

  security_rule {
    name                       = "Allow_HTTPS_Management"
    priority                   = 1002
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "443"
    source_address_prefix      = "**********/16"
    destination_address_prefix = "*"
  }

  security_rule {
    name                       = "Allow_HTTP_Management"
    priority                   = 1003
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "80"
    source_address_prefix      = "**********/16"
    destination_address_prefix = "*"
  }

  security_rule {
    name                       = "Deny_All_Inbound"
    priority                   = 4096
    direction                  = "Inbound"
    access                     = "Deny"
    protocol                   = "*"
    source_port_range          = "*"
    destination_port_range     = "*"
    source_address_prefix      = "*"
    destination_address_prefix = "*"
  }

  tags = var.connectivity_resources_tags
}

# Associate NSG with Management Subnets
resource "azurerm_subnet_network_security_group_association" "management_nsg_associations" {
  for_each = local.management_network_config.management_subnets

  subnet_id                 = azurerm_subnet.management_subnets[each.key].id
  network_security_group_id = azurerm_network_security_group.management_shared_resource_nsg.id
}

# ==============================================================================
# ROUTE TABLES FOR FORCING TRAFFIC THROUGH AZURE FIREWALL
# ==============================================================================

# Associate Route Table with Management Subnets
resource "azurerm_subnet_route_table_association" "management_route_table_associations" {
  for_each = local.management_network_config.management_subnets

  subnet_id      = azurerm_subnet.management_subnets[each.key].id
  route_table_id = azurerm_route_table.management_route_table.id
}

# Resource Group for Route Tables (separate from ALZ managed resources)
resource "azurerm_resource_group" "route_tables" {
  name     = "${var.root_id}-rg-route-tables-${var.primary_location}"
  location = var.primary_location
  tags     = var.connectivity_resources_tags
}

# Route Table for Production Spoke Networks
# Routes traffic through Azure Firewall for security inspection
resource "azurerm_route_table" "production_spoke_route_table" {
  depends_on = [module.alz_connectivity]

  name                = "${var.root_id}-rt-production-spokes"
  location            = azurerm_resource_group.route_tables.location
  resource_group_name = azurerm_resource_group.route_tables.name

  # Route all internet traffic through Azure Firewall
  route {
    name                   = "internet-via-firewall"
    address_prefix         = "0.0.0.0/0"
    next_hop_type          = "VirtualAppliance"
    next_hop_in_ip_address = "**********"  # Azure Firewall private IP
  }

  # Route to other spoke networks through Azure Firewall
  route {
    name                   = "spoke-to-spoke-via-firewall"
    address_prefix         = "**********/16"
    next_hop_type          = "VirtualAppliance"
    next_hop_in_ip_address = "**********"  # Azure Firewall private IP
  }

  # Route to non-production networks through Azure Firewall
  route {
    name                   = "to-nonprod-via-firewall"
    address_prefix         = "**********/16"
    next_hop_type          = "VirtualAppliance"
    next_hop_in_ip_address = "**********"  # Azure Firewall private IP
  }

  # Route to management network through Azure Firewall
  route {
    name                   = "to-management-via-firewall"
    address_prefix         = "***********/21"
    next_hop_type          = "VirtualAppliance"
    next_hop_in_ip_address = "**********"  # Azure Firewall private IP
  }

  tags = var.connectivity_resources_tags
}

# Route Table for Non-Production Spoke Networks
# Routes traffic through Azure Firewall for security inspection
resource "azurerm_route_table" "nonproduction_spoke_route_table" {
  depends_on = [module.alz_connectivity]

  name                = "${var.root_id}-rt-nonproduction-spokes"
  location            = azurerm_resource_group.route_tables.location
  resource_group_name = azurerm_resource_group.route_tables.name

  # Route all internet traffic through Azure Firewall
  route {
    name                   = "internet-via-firewall"
    address_prefix         = "0.0.0.0/0"
    next_hop_type          = "VirtualAppliance"
    next_hop_in_ip_address = "**********"  # Azure Firewall private IP
  }

  # Route to production networks through Azure Firewall
  route {
    name                   = "to-prod-via-firewall"
    address_prefix         = "**********/16"
    next_hop_type          = "VirtualAppliance"
    next_hop_in_ip_address = "**********"  # Azure Firewall private IP
  }

  # Route to other non-production networks through Azure Firewall
  route {
    name                   = "nonprod-to-nonprod-via-firewall"
    address_prefix         = "**********/16"
    next_hop_type          = "VirtualAppliance"
    next_hop_in_ip_address = "**********"  # Azure Firewall private IP
  }

  # Route to management network through Azure Firewall
  route {
    name                   = "to-management-via-firewall"
    address_prefix         = "***********/21"
    next_hop_type          = "VirtualAppliance"
    next_hop_in_ip_address = "**********"  # Azure Firewall private IP
  }

  tags = var.connectivity_resources_tags
}

# Route Table for Management Network
# Routes traffic through Azure Firewall for security inspection
resource "azurerm_route_table" "management_route_table" {
  depends_on = [module.alz_connectivity]

  name                = "${var.root_id}-rt-management"
  location            = azurerm_resource_group.route_tables.location
  resource_group_name = azurerm_resource_group.route_tables.name

  # Route all internet traffic through Azure Firewall
  route {
    name                   = "internet-via-firewall"
    address_prefix         = "0.0.0.0/0"
    next_hop_type          = "VirtualAppliance"
    next_hop_in_ip_address = "**********"  # Azure Firewall private IP
  }

  # Route to production networks through Azure Firewall
  route {
    name                   = "to-prod-via-firewall"
    address_prefix         = "**********/16"
    next_hop_type          = "VirtualAppliance"
    next_hop_in_ip_address = "**********"  # Azure Firewall private IP
  }

  # Route to non-production networks through Azure Firewall
  route {
    name                   = "to-nonprod-via-firewall"
    address_prefix         = "**********/16"
    next_hop_type          = "VirtualAppliance"
    next_hop_in_ip_address = "**********"  # Azure Firewall private IP
  }

  tags = var.connectivity_resources_tags
}

# ==============================================================================
# PEERING BETWEEN HUB AND MANAGEMENT VNETS
# ==============================================================================

# Get hub VNet information from ALZ module
data "azurerm_virtual_network" "hub_vnet" {
  depends_on = [module.alz_connectivity]

  name                = "${var.root_id}-hub-${var.primary_location}"
  resource_group_name = "${var.root_id}-connectivity-${var.primary_location}"
}

# VNet Peering from Management to Hub
resource "azurerm_virtual_network_peering" "management_to_hub" {
  depends_on = [module.alz_connectivity, azurerm_virtual_network.management_vnet]

  name                      = "management-to-hub"
  resource_group_name       = data.azurerm_resource_group.management_network.name
  virtual_network_name      = azurerm_virtual_network.management_vnet.name
  remote_virtual_network_id = data.azurerm_virtual_network.hub_vnet.id

  allow_virtual_network_access = true
  allow_forwarded_traffic      = true
  allow_gateway_transit        = false
  use_remote_gateways          = false
}

# VNet Peering from Hub to Management
resource "azurerm_virtual_network_peering" "hub_to_management" {
  depends_on = [module.alz_connectivity, azurerm_virtual_network.management_vnet]

  name                      = "hub-to-management"
  resource_group_name       = "${var.root_id}-connectivity-${var.primary_location}"
  virtual_network_name      = data.azurerm_virtual_network.hub_vnet.name
  remote_virtual_network_id = azurerm_virtual_network.management_vnet.id

  allow_virtual_network_access = true
  allow_forwarded_traffic      = true
  allow_gateway_transit        = true
  use_remote_gateways          = false
}
