---
name: "Tests (Unit)"

trigger: none

pool:
  vmImage: ubuntu-20.04

variables:
  - group: csu-tf-environment
  - name: PIPELINE_RUN_TYPE
    value: unit

jobs:
  - job: matrix_generator
    displayName: "Matrix Generator"
    steps:
      - template: templates/tests-strategy.yml

  - job: backend_generator
    displayName: "Backend Storage Generator"
    steps:
      - template: templates/tests-backend.yml

  - job: run_unit_tests
    displayName: "Unit Tests"
    dependsOn:
      - matrix_generator
      - backend_generator
    strategy:
      matrix: $[ dependencies.matrix_generator.outputs['build_strategy.matrix_json'] ]
    variables:
      STORAGE_ACCOUNT_RSG_NAME: $[ dependencies.backend_generator.outputs['prepare_backend.STORAGE_ACCOUNT_RSG_NAME'] ]
      STORAGE_ACCOUNT_NAME: $[ dependencies.backend_generator.outputs['prepare_backend.STORAGE_ACCOUNT_NAME'] ]
      STORAGE_CONTAINER_NAME: $[ dependencies.backend_generator.outputs['prepare_backend.STORAGE_CONTAINER_NAME'] ]
    steps:
      - template: templates/tests-common.yml
        parameters:
          run_type: unit

      - task: Bash@3
        displayName: "[terraform fmt]"
        inputs:
          targetType: "inline"
          script: "make tf-fmt"

      - template: templates/tests-loop.yml
        parameters:
          module_path: "tests/modules/test_001_baseline"
          run_type: unit

      - template: templates/tests-loop.yml
        parameters:
          module_path: "tests/modules/test_002_add_custom_core"
          run_type: unit

      - template: templates/tests-loop.yml
        parameters:
          module_path: "tests/modules/test_003_add_mgmt_conn"
          run_type: unit
