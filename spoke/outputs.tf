# Output all spoke configurations
output "spoke_networks" {
  description = "Configuration settings for all deployed spoke networks."
  value = {
    for spoke_key, spoke_config in local.enabled_spokes : spoke_key => {
      resource_group = {
        name     = azurerm_resource_group.spoke_network[spoke_key].name
        id       = azurerm_resource_group.spoke_network[spoke_key].id
        location = azurerm_resource_group.spoke_network[spoke_key].location
      }
      virtual_network = {
        name          = azurerm_virtual_network.spoke_vnet[spoke_key].name
        id            = azurerm_virtual_network.spoke_vnet[spoke_key].id
        address_space = azurerm_virtual_network.spoke_vnet[spoke_key].address_space
      }
      subnets = {
        for subnet_key, subnet_config in spoke_config.subnets : subnet_key => {
          name             = azurerm_subnet.spoke_subnets["${spoke_key}-${subnet_key}"].name
          id               = azurerm_subnet.spoke_subnets["${spoke_key}-${subnet_key}"].id
          address_prefixes = azurerm_subnet.spoke_subnets["${spoke_key}-${subnet_key}"].address_prefixes
          purpose          = try(subnet_config.purpose, "")
          usable_ips       = try(subnet_config.usable_ips, "")
          usable_hosts     = try(subnet_config.usable_hosts, 0)
        }
      }
      nsgs = {
        for subnet_key, subnet_config in spoke_config.subnets : subnet_key => {
          name = azurerm_network_security_group.spoke_nsg["${spoke_key}-${subnet_key}"].name
          id   = azurerm_network_security_group.spoke_nsg["${spoke_key}-${subnet_key}"].id
        }
      }
      peering = {
        spoke_to_hub = {
          name = azurerm_virtual_network_peering.spoke_to_hub[spoke_key].name
          id   = azurerm_virtual_network_peering.spoke_to_hub[spoke_key].id
        }
        hub_to_spoke = {
          name = azurerm_virtual_network_peering.hub_to_spoke[spoke_key].name
          id   = azurerm_virtual_network_peering.hub_to_spoke[spoke_key].id
        }
      }
      configuration = spoke_config
    }
  }
}

# Output enabled spokes summary
output "enabled_spokes_summary" {
  description = "Summary of enabled spoke networks"
  value = {
    count = length(local.enabled_spokes)
    spokes = {
      for spoke_key, spoke_config in local.enabled_spokes : spoke_key => {
        name        = spoke_config.name
        environment = spoke_config.environment
        size        = spoke_config.size
        location    = spoke_config.location
        address_space = spoke_config.address_space
      }
    }
  }
}
