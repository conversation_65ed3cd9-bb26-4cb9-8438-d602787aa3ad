# Core module for Azure Landing Zone
# This module uses the provider configuration from the parent module

# Get the current client configuration from the AzureRM provider.

data "azurerm_client_config" "current" {}

# Local values for configuration
locals {
  # Custom landing zones configuration
  custom_landing_zones = {
    # Productions management group under landing-zones
    "${var.root_id}-lz-prd" = {
      display_name               = "Productions"
      parent_management_group_id = "${var.root_id}-landing-zones"
      subscription_ids           = []
      archetype_config = {
        archetype_id   = "productions"
        parameters     = {}
        access_control = {}
      }
    }

    # Non-Productions management group under landing-zones
    "${var.root_id}-lz-non-prd" = {
      display_name               = "Non-Productions"
      parent_management_group_id = "${var.root_id}-landing-zones"
      subscription_ids           = []
      archetype_config = {
        archetype_id   = "non_productions"
        parameters     = {}
        access_control = {}
      }
    }

    # Product A under Productions
    "${var.root_id}-lz-prd-legacy" = {
      display_name               = "lz-prd-legacy"
      parent_management_group_id = "${var.root_id}-lz-prd"
      subscription_ids           = []
      archetype_config = {
        archetype_id   = "product_a"
        parameters     = {}
        access_control = {}
      }
    }

    # Product B under Productions
    "${var.root_id}-lz-prd-microsc" = {
      display_name               = "lz-prd-microsc"
      parent_management_group_id = "${var.root_id}-lz-prd"
      subscription_ids           = []
      archetype_config = {
        archetype_id   = "product_b"
        parameters     = {}
        access_control = {}
      }
    }

    # UAT under Non-Productions
    "${var.root_id}-lz-non-prd-uat" = {
      display_name               = "lz-non-prd-uat"
      parent_management_group_id = "${var.root_id}-lz-non-prd"
      subscription_ids           = []
      archetype_config = {
        archetype_id   = "uat"
        parameters     = {}
        access_control = {}
      }
    }

    # DevTest under Non-Productions
    "${var.root_id}-lz-non-prd-dev" = {
      display_name               = "lz-non-prd-dev"
      parent_management_group_id = "${var.root_id}-lz-non-prd"
      subscription_ids           = []
      archetype_config = {
        archetype_id   = "devtest"
        parameters     = {}
        access_control = {}
      }
    }
  }

  # Identity resources configuration
  configure_identity_resources = {
    settings = {
      identity = {
        enabled = true
        config = {
          enable_deny_public_ip             = true
          enable_deny_rdp_from_internet     = true
          enable_deny_subnet_without_nsg    = true
          enable_deploy_azure_backup_on_vms = true
        }
      }
    }
  }
}

# Declare the Azure landing zones Terraform module
# and provide the core configuration for management groups and policies only.

module "alz" {
  source  = "Azure/caf-enterprise-scale/azurerm"
  version = "6.2.0" # change this to your desired version, https://www.terraform.io/language/expressions/version-constraints

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm
    azurerm.management   = azurerm
  }
  default_location = var.primary_location

  # Base module configuration settings
  root_parent_id = data.azurerm_client_config.current.tenant_id
  root_id        = var.root_id
  root_name      = var.root_name
  library_path   = "${path.module}/lib"

  # Enable creation of the core management group hierarchy
  # and additional custom_landing_zones
  deploy_core_landing_zones = true
  custom_landing_zones      = local.custom_landing_zones

  # Configuration settings for identity resources is
  # bundled with core as no resources are actually created
  # for the identity subscription
  deploy_identity_resources    = true
  configure_identity_resources = local.configure_identity_resources
  subscription_id_identity     = var.subscription_id_identity

  # Disable connectivity and management resource deployment
  # These will be handled by separate modules
  deploy_connectivity_resources = false
  deploy_management_resources   = false

  # Disable custom role definitions due to insufficient permissions
  disable_telemetry = true

  # Map subscriptions to management groups without deploying resources
  subscription_id_connectivity = var.subscription_id_connectivity
  subscription_id_management   = var.subscription_id_management

  # Disable the problematic policy set definition by excluding it
  disable_base_module_tags = true

}
