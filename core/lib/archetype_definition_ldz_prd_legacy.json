{"ldz_prd_legacy": {"policy_assignments": ["Deny-Resource-Locations", "Deny-RSG-Locations", "Secure-Storage-Accounts", "Enforce-NSG", "Block-Public-IP-VMs", "Deny-KV-Not-Recoverable", "Deny-Storage-Public", "Deny-VM-Internet-SSH-RDP", "Enforce-Naming", "Require-Mandatory-Tags", "Enable-Monitoring", "Enable-Backup", "Enforce-VM-Security"], "policy_definitions": ["enforce_network_security_groups", "require_mandatory_tags_with_rules", "secure_storage_accounts", "naming_convention", "enforce_vm_security_legacy", "enable_monitoring_legacy"], "policy_set_definitions": ["Enforce-Legacy-Application-Security"], "role_definitions": ["Legacy-Application-Operator"], "archetype_config": {"parameters": {"Deny-Resource-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia"]}, "Deny-RSG-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia"]}, "Require-Mandatory-Tags": {"Owner": "Owner", "org": "Organization", "created_by": "CreatedBy", "operation_team": "OperationTeam", "project_name": "ProjectName", "env": "Production", "app_name": "ApplicationName", "resource_type": "ResourceType", "priority": "High", "data_zone": "Internal", "cost_center": "CostCenter", "managed_by": "ManagedBy", "architecture": "Legacy"}}, "access_control": {"Legacy-Application-Operator": ["Virtual Machine Contributor", "Storage Account Contributor", "SQL DB Contributor"]}}}}