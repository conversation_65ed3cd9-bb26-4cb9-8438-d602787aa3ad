{"product_a": {"policy_assignments": [], "policy_definitions": [], "policy_set_definitions": [], "role_definitions": [], "archetype_config": {"parameters": {"Deny-Resource-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia"]}, "Deny-RSG-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia"]}, "Require-Mandatory-Tags": {"Owner": "Owner", "org": "Organization", "created_by": "CreatedBy", "operation_team": "OperationTeam", "project_name": "ProjectName", "env": "Environment", "app_name": "ApplicationName", "resource_type": "ResourceType", "priority": "Priority", "data_zone": "Public"}}, "access_control": {}}}}