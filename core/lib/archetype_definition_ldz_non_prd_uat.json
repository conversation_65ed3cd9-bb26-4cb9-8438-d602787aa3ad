{"ldz_non_prd_uat": {"policy_assignments": ["Deny-Resource-Locations", "Deny-RSG-Locations", "Secure-Storage-Accounts", "Enforce-NSG", "Enforce-Naming", "Require-Mandatory-Tags", "Enable-Monitoring"], "policy_definitions": ["enforce_network_security_groups", "require_mandatory_tags_with_rules", "secure_storage_accounts", "naming_convention", "enable_monitoring_uat"], "policy_set_definitions": ["Enforce-UAT-Security"], "role_definitions": ["UAT-Application-Operator", "UAT-Tester"], "archetype_config": {"parameters": {"Deny-Resource-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia"]}, "Deny-RSG-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia"]}, "Require-Mandatory-Tags": {"Owner": "Owner", "org": "Organization", "created_by": "CreatedBy", "operation_team": "OperationTeam", "project_name": "ProjectName", "env": "UAT", "app_name": "ApplicationName", "resource_type": "ResourceType", "priority": "Medium", "data_zone": "Internal", "cost_center": "CostCenter", "managed_by": "ManagedBy"}}, "access_control": {"UAT-Application-Operator": ["Contributor"], "UAT-Tester": ["Reader", "Application Insights Component Contributor"]}}}}