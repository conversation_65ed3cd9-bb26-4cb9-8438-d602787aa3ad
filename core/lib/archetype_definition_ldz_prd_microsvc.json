{"ldz_prd_microsvc": {"policy_assignments": ["Deny-Resource-Locations", "Deny-RSG-Locations", "Secure-Storage-Accounts", "Enforce-NSG", "Block-Public-IP-VMs", "Deny-KV-Not-Recoverable", "Deny-Storage-Public", "Enforce-Naming", "Require-Mandatory-Tags", "Enable-Monitoring", "Enable-Backup", "Enforce-Container-Security", "Enable-Service-Mesh"], "policy_definitions": ["enforce_network_security_groups", "require_mandatory_tags_with_rules", "secure_storage_accounts", "naming_convention", "enforce_container_security", "enable_monitoring_microservices", "enable_service_mesh_security"], "policy_set_definitions": ["Enforce-Microservices-Security", "Enforce-Container-Compliance"], "role_definitions": ["Microservices-Application-Operator", "Container-Security-Operator"], "archetype_config": {"parameters": {"Deny-Resource-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia"]}, "Deny-RSG-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia"]}, "Require-Mandatory-Tags": {"Owner": "Owner", "org": "Organization", "created_by": "CreatedBy", "operation_team": "OperationTeam", "project_name": "ProjectName", "env": "Production", "app_name": "ApplicationName", "resource_type": "ResourceType", "priority": "High", "data_zone": "Internal", "cost_center": "CostCenter", "managed_by": "ManagedBy", "architecture": "Microservices"}}, "access_control": {"Microservices-Application-Operator": ["Azure Kubernetes Service Contributor", "Container Registry Contributor", "Application Insights Component Contributor"], "Container-Security-Operator": ["Security Admin", "Azure Kubernetes Service RBAC Admin"]}}}}