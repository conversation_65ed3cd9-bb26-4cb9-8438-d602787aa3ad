{"ldz_non_prd_dev": {"policy_assignments": ["Deny-Resource-Locations", "Deny-RSG-Locations", "Enforce-Naming", "Require-Mandatory-Tags", "Enable-Monitoring"], "policy_definitions": ["require_mandatory_tags_with_rules", "naming_convention", "enable_monitoring_dev"], "policy_set_definitions": ["Enforce-Development-Security"], "role_definitions": ["Development-Application-Operator", "Developer"], "archetype_config": {"parameters": {"Deny-Resource-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia"]}, "Deny-RSG-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia"]}, "Require-Mandatory-Tags": {"Owner": "Owner", "org": "Organization", "created_by": "CreatedBy", "operation_team": "OperationTeam", "project_name": "ProjectName", "env": "Development", "app_name": "ApplicationName", "resource_type": "ResourceType", "priority": "Low", "data_zone": "Internal", "cost_center": "CostCenter", "managed_by": "ManagedBy"}}, "access_control": {"Development-Application-Operator": ["Contributor"], "Developer": ["Contributor", "DevTest Labs User"]}}}}