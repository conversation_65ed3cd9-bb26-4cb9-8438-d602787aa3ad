{"ldz_non_production": {"policy_assignments": ["Deny-Resource-Locations", "Deny-RSG-Locations", "Secure-Storage-Accounts", "Enforce-NSG", "Deny-KV-Not-Recoverable", "Enforce-Naming", "Require-Mandatory-Tags", "Enable-Monitoring"], "policy_definitions": ["enforce_network_security_groups", "require_mandatory_tags_with_rules", "secure_storage_accounts", "naming_convention", "enable_monitoring_non_production"], "policy_set_definitions": ["Enforce-Non-Production-Security"], "role_definitions": ["Non-Production-Application-Operator"], "archetype_config": {"parameters": {"Deny-Resource-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia"]}, "Deny-RSG-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia"]}, "Require-Mandatory-Tags": {"Owner": "Owner", "org": "Organization", "created_by": "CreatedBy", "operation_team": "OperationTeam", "project_name": "ProjectName", "env": "Environment", "app_name": "ApplicationName", "resource_type": "ResourceType", "priority": "Medium", "data_zone": "Internal", "cost_center": "CostCenter", "managed_by": "ManagedBy"}}, "access_control": {"Non-Production-Application-Operator": ["Contributor"]}}}}