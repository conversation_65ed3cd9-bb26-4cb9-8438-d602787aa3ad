{"ldz_production": {"policy_assignments": ["Deny-Resource-Locations", "Deny-RSG-Locations", "Secure-Storage-Accounts", "Enforce-NSG", "Block-Public-IP-VMs", "Deny-KV-Not-Recoverable", "Deny-Storage-Public", "Deny-VM-Internet-SSH-RDP", "Enforce-Naming", "Require-Mandatory-Tags", "Enable-Monitoring", "Enable-Backup", "Enforce-Encryption", "Enable-Security-Center"], "policy_definitions": ["enforce_network_security_groups", "require_mandatory_tags_with_rules", "secure_storage_accounts", "naming_convention", "enforce_encryption_production", "enable_monitoring_production", "enable_backup_production"], "policy_set_definitions": ["Enforce-Production-Security", "Enforce-Production-Compliance"], "role_definitions": ["Production-Application-Operator", "Production-Security-Operator"], "archetype_config": {"parameters": {"Deny-Resource-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia"]}, "Deny-RSG-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia"]}, "Require-Mandatory-Tags": {"Owner": "Owner", "org": "Organization", "created_by": "CreatedBy", "operation_team": "OperationTeam", "project_name": "ProjectName", "env": "Production", "app_name": "ApplicationName", "resource_type": "ResourceType", "priority": "High", "data_zone": "Internal", "cost_center": "CostCenter", "managed_by": "ManagedBy"}}, "access_control": {"Production-Application-Operator": ["Contributor"], "Production-Security-Operator": ["Security Reader", "Security Admin"]}}}}