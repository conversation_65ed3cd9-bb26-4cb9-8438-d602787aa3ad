{"platform_connectivity": {"policy_assignments": ["Deny-Resource-Locations", "Deny-RSG-Locations", "Secure-Storage-Accounts", "Enforce-NSG", "Block-Public-IP-VMs", "Deny-KV-Not-Recoverable", "Deny-Storage-Public", "Enforce-Naming", "Require-Mandatory-Tags", "Enable-Network-Monitoring", "Enforce-Firewall-Rules"], "policy_definitions": ["enforce_network_security_groups", "require_mandatory_tags_with_rules", "secure_storage_accounts", "naming_convention", "enforce_firewall_rules", "enable_network_monitoring"], "policy_set_definitions": ["Enforce-Platform-Connectivity"], "role_definitions": ["Platform-Connectivity-Operator", "Network-Security-Operator"], "archetype_config": {"parameters": {"Deny-Resource-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia"]}, "Deny-RSG-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia"]}, "Require-Mandatory-Tags": {"Owner": "Owner", "org": "Organization", "created_by": "CreatedBy", "operation_team": "OperationTeam", "project_name": "ProjectName", "env": "Environment", "app_name": "ApplicationName", "resource_type": "ResourceType", "priority": "Priority", "data_zone": "Internal", "cost_center": "CostCenter", "managed_by": "ManagedBy"}}, "access_control": {"Platform-Connectivity-Operator": ["Network Contributor", "DNS Zone Contributor", "Application Gateway Contributor"], "Network-Security-Operator": ["Security Admin", "Network Security Group Contributor"]}}}}