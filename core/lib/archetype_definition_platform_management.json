{"platform_management": {"policy_assignments": ["Deny-Resource-Locations", "Deny-RSG-Locations", "Secure-Storage-Accounts", "Enforce-NSG", "Block-Public-IP-VMs", "Deny-KV-Not-Recoverable", "Deny-Storage-Public", "Deny-VM-Internet-SSH-RDP", "Enforce-Naming", "Require-Mandatory-Tags", "Enable-Monitoring", "Enable-Backup"], "policy_definitions": ["enforce_network_security_groups", "require_mandatory_tags_with_rules", "secure_storage_accounts", "naming_convention", "enable_monitoring_platform", "enable_backup_platform"], "policy_set_definitions": ["Enforce-Platform-Management"], "role_definitions": ["Platform-Management-Operator"], "archetype_config": {"parameters": {"Deny-Resource-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia"]}, "Deny-RSG-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia"]}, "Require-Mandatory-Tags": {"Owner": "Owner", "org": "Organization", "created_by": "CreatedBy", "operation_team": "OperationTeam", "project_name": "ProjectName", "env": "Environment", "app_name": "ApplicationName", "resource_type": "ResourceType", "priority": "Priority", "data_zone": "Internal", "cost_center": "CostCenter", "managed_by": "ManagedBy"}}, "access_control": {"Platform-Management-Operator": ["Contributor", "Key Vault Administrator", "Log Analytics Contributor", "Monitoring Contributor"]}}}}