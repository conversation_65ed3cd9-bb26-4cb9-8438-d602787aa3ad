# Cấu Trúc Management Group Mới

## 🏗️ Tổng Quan Cấu Trúc

Cấu trúc Management Group đã được thiết kế lại theo yêu cầu mới với phân chia rõ ràng theo chức năng và môi trường:

```
Root Management Group (ewh)
├── Platform
│   ├── mg-Platform-Management
│   └── mg-Platform-Connectivity
├── Landing Zone
│   ├── ldz-prd (Production)
│   │   ├── ldz-prd-legacy
│   │   └── ldz-prd-microsvc
│   └── ldz-non-prd (Non-Production)
│       ├── ldz-non-prd-uat
│       └── ldz-non-prd-dev
├── Sandbox
└── Decommissioned
```

## 📋 Chi Tiết Từng Management Group

### 🔧 Platform Management Groups

#### **mg-Platform-Management**
- **Mục đích**: Chứa các subscription có tài nguyên dùng chung cho các subscription khác
- **Tài nguyên**: Azure Key Vaults, Azure Monitor, Log Analytics
- **Quản lý bởi**: Team Hạ tầng
- **Archetype**: `platform_management`
- **Policies áp dụng**:
  - Enforce mandatory tags
  - Secure storage accounts
  - Enable monitoring và backup
  - Network security groups

#### **mg-Platform-Connectivity**
- **Mục đích**: Cung cấp dịch vụ kết nối và tài nguyên mạng dùng chung
- **Tài nguyên**: Azure Firewall, Application Gateway, DNS, VPN Gateway
- **Quản lý bởi**: Teams Hạ tầng, Mạng và ANBM
- **Archetype**: `platform_connectivity`
- **Policies áp dụng**:
  - Network monitoring
  - Firewall rules enforcement
  - DNS security
  - Network security groups

### 🚀 Landing Zone Management Groups

#### **ldz-prd (Production)**
- **Mục đích**: Chứa các subscription môi trường production
- **Archetype**: `ldz_production`
- **Policies áp dụng**:
  - High security requirements
  - Encryption enforcement
  - Backup mandatory
  - Security Center enabled

##### **ldz-prd-legacy**
- **Mục đích**: Subscription cho ứng dụng kiến trúc truyền thống (monolithic)
- **Archetype**: `ldz_prd_legacy`
- **Tài nguyên**: Virtual Machines, SQL Servers, Storage Accounts
- **Policies đặc biệt**:
  - VM security enforcement
  - Legacy application monitoring

##### **ldz-prd-microsvc**
- **Mục đích**: Subscription cho ứng dụng kiến trúc microservices
- **Archetype**: `ldz_prd_microsvc`
- **Tài nguyên**: AKS, Container Registry, Service Mesh
- **Policies đặc biệt**:
  - Container security
  - Service mesh security
  - Kubernetes RBAC

#### **ldz-non-prd (Non-Production)**
- **Mục đích**: Chứa các subscription môi trường ngoài production
- **Archetype**: `ldz_non_production`
- **Policies áp dụng**:
  - Relaxed security (so với production)
  - Cost optimization
  - Basic monitoring

##### **ldz-non-prd-uat**
- **Mục đích**: Subscription cho môi trường UAT
- **Archetype**: `ldz_non_prd_uat`
- **Policies đặc biệt**:
  - UAT-specific monitoring
  - Testing tools access

##### **ldz-non-prd-dev**
- **Mục đích**: Subscription cho môi trường Development
- **Archetype**: `ldz_non_prd_dev`
- **Policies đặc biệt**:
  - Developer access
  - DevTest Labs
  - Minimal restrictions

### 🧪 Sandbox
- **Mục đích**: Subscription cho môi trường sandbox
- **Archetype**: `es_sandboxes` (sử dụng archetype có sẵn)
- **Policies**: Minimal restrictions, experimentation allowed

### 🗑️ Decommissioned
- **Mục đích**: Subscription dự kiến ngừng sử dụng
- **Archetype**: `es_decommissioned` (sử dụng archetype có sẵn)
- **Policies**: Deny all new resources, cleanup policies

## 🔐 Phân Quyền và Access Control

### Platform Management Groups
- **Platform-Management-Operator**: Contributor, Key Vault Admin, Log Analytics Contributor
- **Platform-Connectivity-Operator**: Network Contributor, DNS Zone Contributor
- **Network-Security-Operator**: Security Admin, NSG Contributor

### Production Landing Zones
- **Production-Application-Operator**: Contributor (với restrictions)
- **Production-Security-Operator**: Security Reader/Admin
- **Legacy-Application-Operator**: VM Contributor, Storage Contributor
- **Microservices-Application-Operator**: AKS Contributor, Container Registry Contributor

### Non-Production Landing Zones
- **Non-Production-Application-Operator**: Contributor
- **UAT-Tester**: Reader, Application Insights Contributor
- **Developer**: Contributor, DevTest Labs User

## 📊 Policy Inheritance

Policies được kế thừa từ parent management groups:

1. **Root Level**: Basic governance policies
2. **Platform Level**: Infrastructure-specific policies
3. **Landing Zone Level**: Environment-specific policies
4. **Sub-Level**: Workload-specific policies

## 🚀 Triển Khai

Để triển khai cấu trúc mới:

1. **Cập nhật variables**:
   ```bash
   # Trong terraform.tfvars
   root_id = "ewh"
   root_name = "EWH"
   ```

2. **Deploy core module**:
   ```bash
   terraform plan -target=module.core
   terraform apply -target=module.core
   ```

3. **Verify management groups**:
   - Kiểm tra trong Azure Portal
   - Xác nhận policy assignments
   - Verify subscription mappings

## ⚠️ Lưu Ý Quan Trọng

1. **Breaking Changes**: Cấu trúc mới khác hoàn toàn với cấu trúc cũ
2. **Migration**: Cần plan migration cho subscriptions hiện tại
3. **Policies**: Một số policies mới có thể cần custom definitions
4. **Permissions**: Cần review và update role assignments

## 🔄 Migration Plan

1. **Phase 1**: Deploy new management group structure
2. **Phase 2**: Create new archetype definitions
3. **Phase 3**: Move subscriptions to new management groups
4. **Phase 4**: Update policy assignments
5. **Phase 5**: Clean up old structure
