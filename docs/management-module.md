# Management Module - Log Analytics và Security Center

Management Module chịu trách nhiệm thiết lập hạ tầng giám sát, logging, và bảo mật cho toàn bộ Azure Landing Zone.

## 🎯 Mục Đích

- <PERSON><PERSON>n khai Log Analytics Workspace tập trung
- <PERSON><PERSON><PERSON> hình Microsoft Defender for Cloud
- Thiết lập Azure Monitor và alerting
- Quản lý Data Collection Rules (DCR)
- Cấu hình Microsoft Sentinel (tùy chọn)

## 📋 Biến Cấu Hình

### Biến Bắt <PERSON>

```hcl
# Cấu hình cơ bản
root_id = "ewh"
# Mô tả: Tiền tố tổ chức, phải khớp với Core Module

root_name = "EWH"
# Mô tả: Tên hiển thị tổ chức

primary_location = "southeastasia"
# Mô tả: Vùng Azure chính để triển khai tài nguyên

location = "southeastasia"
# Mô tả: V<PERSON>ng cụ thể cho management resources

subscription_id_management = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
# Mô tả: Subscription ID cho management resources

security_alerts_email_address = "<EMAIL>"
# Mô tả: Email nhận cảnh báo bảo mật từ Microsoft Defender for Cloud

log_retention_in_days = 60
# Mô tả: Số ngày lưu trữ log trong Log Analytics Workspace
# Giá trị: 30-730 ngày (tùy theo yêu cầu compliance)
```

### Biến Tùy Chọn - Cấu Hình Nâng Cao

```hcl
# Azure Monitor Agent (AMA) Configuration
enable_ama = true
# Mô tả: Bật Azure Monitor Agent cho VM monitoring
# Khuyến nghị: true cho môi trường production

enable_vminsights_dcr = true
# Mô tả: Bật VM Insights Data Collection Rule
# Yêu cầu: enable_ama = true

enable_change_tracking_dcr = true
# Mô tả: Bật Change Tracking Data Collection Rule
# Mục đích: Theo dõi thay đổi cấu hình VM

enable_mdfc_defender_for_sql_dcr = false
# Mô tả: Bật Microsoft Defender for SQL Data Collection Rule
# Chỉ bật nếu có SQL workloads

# VM Monitoring
enable_monitoring_for_vm = true
# Mô tả: Bật monitoring cho Virtual Machines

enable_monitoring_for_vmss = true
# Mô tả: Bật monitoring cho Virtual Machine Scale Sets

# Security Solutions
enable_sentinel = false
# Mô tả: Bật Microsoft Sentinel SIEM
# Lưu ý: Có chi phí cao, cân nhắc kỹ trước khi bật

enable_change_tracking = true
# Mô tả: Bật Change Tracking solution
```

### Microsoft Defender for Cloud Configuration

```hcl
# Defender Plans - Cấu hình chi tiết
enable_defender_for_servers = true
# Mô tả: Bảo vệ servers và VMs
# Chi phí: ~$15/server/tháng

enable_defender_for_app_services = true
# Mô tả: Bảo vệ Azure App Services
# Chi phí: ~$15/App Service plan/tháng

enable_defender_for_containers = true
# Mô tả: Bảo vệ containers và Kubernetes
# Chi phí: ~$7/vCore/tháng

enable_defender_for_storage = true
# Mô tả: Bảo vệ Azure Storage accounts
# Chi phí: ~$10/storage account/tháng

enable_defender_for_sql_servers = true
# Mô tả: Bảo vệ Azure SQL Databases
# Chi phí: ~$15/server/tháng

enable_defender_for_sql_server_vms = true
# Mô tả: Bảo vệ SQL Server trên VMs
# Chi phí: ~$15/server/tháng

enable_defender_for_key_vault = true
# Mô tả: Bảo vệ Azure Key Vault
# Chi phí: ~$2/10K transactions/tháng

enable_defender_for_arm = true
# Mô tả: Bảo vệ Azure Resource Manager
# Chi phí: Miễn phí

enable_defender_for_dns = true
# Mô tả: Bảo vệ DNS queries
# Chi phí: ~$2/million queries/tháng

enable_defender_for_oss_databases = false
# Mô tả: Bảo vệ Open Source Databases
# Chỉ bật nếu sử dụng PostgreSQL, MySQL, MariaDB

enable_defender_for_cosmosdbs = false
# Mô tả: Bảo vệ Azure Cosmos DB
# Chỉ bật nếu sử dụng Cosmos DB

enable_defender_for_apis = false
# Mô tả: Bảo vệ APIs
# Tính năng mới, cân nhắc trước khi bật

enable_defender_for_cspm = true
# Mô tả: Cloud Security Posture Management
# Khuyến nghị: true cho compliance

# Vulnerability Assessment
enable_defender_for_servers_vulnerability_assessments = true
# Mô tả: Đánh giá lỗ hổng bảo mật cho servers
# Yêu cầu: enable_defender_for_servers = true
```

### Tags Configuration

```hcl
tags = {
  # Mandatory Tags
  Owner           = "<EMAIL>"
  Organization    = "EWH"
  CreatedBy       = "<EMAIL>"
  OperationTeam   = "Platform Team"
  ProjectName     = "EWH Landing Zone"
  Environment     = "Production"
  ApplicationName = "Management Infrastructure"
  ResourceType    = "Management"
  Priority        = "High"
  DataZone        = "Internal"
  CostCenter      = "IT-001"
  ManagedBy       = "Terraform"
  
  # Additional Tags
  BusinessUnit    = "IT"
  Compliance      = "ISO27001"
  BackupRequired  = "Yes"
  Monitoring      = "Yes"
  DRPlan          = "Yes"
}
```

## 🏗️ Tài Nguyên Được Tạo

### Log Analytics Workspace

- **Tên**: `{root_id}-log-analytics`
- **Vùng**: `primary_location`
- **Retention**: Theo `log_retention_in_days`
- **SKU**: PerGB2018 (Pay-as-you-go)

### Microsoft Defender for Cloud

- Security Center workspace configuration
- Defender plans theo cấu hình
- Security contacts và notifications
- Auto-provisioning settings

### Data Collection Rules (DCR)

- **VM Insights DCR**: Thu thập performance metrics
- **Change Tracking DCR**: Theo dõi thay đổi cấu hình
- **Security DCR**: Thu thập security events

### Automation Account (nếu cần)

- Change Tracking solution
- Update Management (deprecated, khuyến nghị dùng Azure Update Manager)

## 🔧 Cách Cấu Hình

### 1. Cấu Hình Cơ Bản (Khuyến Nghị Cho Bắt Đầu)

```hcl
# terraform.tfvars
root_id                        = "ewh"
root_name                      = "EWH"
primary_location               = "southeastasia"
location                       = "southeastasia"
subscription_id_management     = "your-management-subscription-id"
security_alerts_email_address = "<EMAIL>"
log_retention_in_days          = 60

# Cấu hình cơ bản - tắt các tính năng tốn kém
enable_ama                           = false
enable_vminsights_dcr                = false
enable_change_tracking_dcr           = false
enable_sentinel                      = false

# Defender for Cloud - chỉ bật cơ bản
enable_defender_for_servers          = true
enable_defender_for_arm              = true
enable_defender_for_cspm             = true
```

### 2. Cấu Hình Production (Đầy Đủ Tính Năng)

```hcl
# Bật tất cả monitoring
enable_ama                           = true
enable_vminsights_dcr                = true
enable_change_tracking_dcr           = true
enable_monitoring_for_vm             = true
enable_monitoring_for_vmss           = true

# Bật tất cả Defender plans
enable_defender_for_servers                           = true
enable_defender_for_app_services                      = true
enable_defender_for_containers                        = true
enable_defender_for_storage                           = true
enable_defender_for_sql_servers                       = true
enable_defender_for_sql_server_vms                    = true
enable_defender_for_key_vault                         = true
enable_defender_for_arm                               = true
enable_defender_for_dns                               = true
enable_defender_for_cspm                              = true
enable_defender_for_servers_vulnerability_assessments = true

# Sentinel (cân nhắc chi phí)
enable_sentinel = false  # Bật sau khi đánh giá chi phí
```

### 3. Cấu Hình Theo Workload

```hcl
# Nếu có SQL workloads
enable_defender_for_sql_servers    = true
enable_defender_for_sql_server_vms = true
enable_mdfc_defender_for_sql_dcr   = true

# Nếu có Container workloads
enable_defender_for_containers = true

# Nếu có Cosmos DB
enable_defender_for_cosmosdbs = true

# Nếu có nhiều APIs
enable_defender_for_apis = true
```

## 💰 Ước Tính Chi Phí

### Log Analytics Workspace

- **Data Ingestion**: ~$2.30/GB
- **Data Retention**: ~$0.10/GB/tháng (sau 31 ngày đầu miễn phí)
- **Ước tính**: 10-50GB/tháng cho môi trường nhỏ-vừa

### Microsoft Defender for Cloud

| Plan | Chi Phí (USD/tháng) | Ghi Chú |
|------|---------------------|---------|
| Servers | ~$15/server | Bắt buộc cho production |
| App Services | ~$15/plan | Nếu có App Services |
| Containers | ~$7/vCore | Nếu có AKS/containers |
| Storage | ~$10/account | Khuyến nghị bật |
| SQL | ~$15/server | Nếu có SQL workloads |
| Key Vault | ~$2/10K transactions | Chi phí thấp |

### Microsoft Sentinel

- **Data Ingestion**: ~$2.30/GB
- **Ước tính**: $500-2000/tháng tùy theo data volume

## 🚀 Triển Khai

```bash
# Triển khai Management Module (sau Core Module)
terraform plan -target=module.management
terraform apply -target=module.management

# Kiểm tra outputs
terraform output management_outputs
```

## 🔍 Kiểm Tra Sau Triển Khai

1. **Log Analytics**: Kiểm tra workspace trong Azure Portal
2. **Defender for Cloud**: Xác nhận plans được bật
3. **Security Contacts**: Kiểm tra email notifications
4. **Data Collection**: Xác nhận DCRs hoạt động
5. **Alerts**: Test security alerts

## ⚠️ Lưu Ý Quan Trọng

- **Chi phí**: Defender for Cloud có thể tốn kém, bật từng plan theo nhu cầu
- **Data retention**: Cân nhắc compliance requirements
- **Security contacts**: Đảm bảo email hoạt động 24/7
- **Monitoring**: Thiết lập alerts cho chi phí bất thường
