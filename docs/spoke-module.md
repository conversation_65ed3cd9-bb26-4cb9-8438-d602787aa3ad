# Spoke Module - Application Networks

Spoke Module chịu trách nhiệm triển khai mạng spoke cho các workload <PERSON>ng dụng, kết nối với hub network thông qua VNet peering.

## 🎯 Mục Đích

- <PERSON><PERSON>n khai Spoke Virtual Networks cho workloads
- Thiết lập VNet Peering với Hub Network
- Cấu hình Network Security Groups (NSGs)
- Quản lý Route Tables cho traffic routing
- Tạo subnets cho các tiers ứng dụng khác nhau

## 📋 Biến Cấu Hình

### Biến Bắt Buộc

```hcl
# Cấu hình cơ bản
root_id = "ewh"
# Mô tả: Tiền tố tổ chức, phải khớp với các module khác

spoke_name = "product_a"
# Mô tả: Tên spoke network, phải khớp với key trong settings.spoke.tf
# Các giá trị hợp lệ: "product_a", "product_microservice", "product_c", "uat", "devtest_a"

location = "southeastasia"
# Mô tả: Vùng Azure để triển khai spoke network

# Hub Network Information (từ Connectivity Module outputs)
hub_vnet_id = "/subscriptions/{subscription-id}/resourceGroups/rg-connectivity-sea/providers/Microsoft.Network/virtualNetworks/vnet-hub-sea"
# Mô tả: Resource ID của hub virtual network

hub_vnet_name = "vnet-hub-sea"
# Mô tả: Tên của hub virtual network

hub_resource_group_name = "rg-connectivity-sea"
# Mô tả: Resource group name của hub virtual network

hub_address_space = "**********/16"
# Mô tả: Address space của hub network (cho NSG rules)

management_address_space = "***********/21"
# Mô tả: Address space của management subnet (cho NSG rules)
```

### Biến Tùy Chọn

```hcl
# VNet Peering Configuration
use_remote_gateways = false
# Mô tả: Sử dụng gateway của hub network
# Chỉ bật nếu hub có VPN Gateway và spoke cần kết nối on-premises

allow_gateway_transit = true
# Mô tả: Cho phép hub forward traffic qua gateway
# Khuyến nghị: true để hub có thể route traffic

# Route Table Configuration
route_table_id = null
# Mô tả: Resource ID của route table để associate với spoke subnets
# Để null nếu chưa có route table

enable_route_table_association = false
# Mô tả: Bật association route table với subnets
# Khuyến nghị: false trong lần deploy đầu, bật sau khi có route table

# Tags
spoke_resources_tags = {
  deployedBy = "CTS"
  type       = "spoke-resources"
  workload   = "application"
  tier       = "production"
}
# Mô tả: Tags riêng cho spoke resources
```

## 🏗️ Spoke Network Configurations

### Các Spoke Networks Có Sẵn

File `spoke/settings.spoke.tf` định nghĩa các spoke networks:

#### 1. Product A (product_a)
```hcl
product_a = {
  name                = "vnet-spoke-product-a-sea"
  resource_group_name = "rg-spoke-product-a-sea"
  address_space       = ["**********/16"]
  
  subnets = {
    web = {
      address_prefixes = ["**********/24"]
      service_endpoints = ["Microsoft.Storage", "Microsoft.KeyVault"]
    }
    app = {
      address_prefixes = ["**********/24"]
      service_endpoints = ["Microsoft.Storage", "Microsoft.Sql"]
    }
    data = {
      address_prefixes = ["**********/24"]
      service_endpoints = ["Microsoft.Sql", "Microsoft.Storage"]
    }
    integration = {
      address_prefixes = ["**********/24"]
      service_endpoints = ["Microsoft.ServiceBus", "Microsoft.EventHub"]
    }
  }
}
```

#### 2. Microservices Platform (product_microservice)
```hcl
product_microservice = {
  name                = "vnet-spoke-microservice-sea"
  resource_group_name = "rg-spoke-microservice-sea"
  address_space       = ["**********/16"]
  
  subnets = {
    aks_system = {
      address_prefixes = ["**********/24"]
      service_endpoints = ["Microsoft.ContainerRegistry"]
    }
    aks_workload = {
      address_prefixes = ["**********/23"]  # Larger subnet for pods
      service_endpoints = ["Microsoft.Storage", "Microsoft.KeyVault"]
    }
    api_management = {
      address_prefixes = ["**********/24"]
      service_endpoints = ["Microsoft.Storage"]
    }
    integration = {
      address_prefixes = ["**********/24"]
      service_endpoints = ["Microsoft.ServiceBus", "Microsoft.EventHub"]
    }
  }
}
```

#### 3. Product C (product_c)
```hcl
product_c = {
  name                = "vnet-spoke-product-c-sea"
  resource_group_name = "rg-spoke-product-c-sea"
  address_space       = ["**********/16"]
  
  subnets = {
    frontend = {
      address_prefixes = ["**********/24"]
      service_endpoints = ["Microsoft.Storage", "Microsoft.KeyVault"]
    }
    backend = {
      address_prefixes = ["**********/24"]
      service_endpoints = ["Microsoft.Storage", "Microsoft.Sql"]
    }
    database = {
      address_prefixes = ["**********/24"]
      service_endpoints = ["Microsoft.Sql"]
    }
  }
}
```

#### 4. UAT Environment (uat)
```hcl
uat = {
  name                = "vnet-spoke-uat-sea"
  resource_group_name = "rg-spoke-uat-sea"
  address_space       = ["**********/16"]
  
  subnets = {
    web = {
      address_prefixes = ["**********/24"]
    }
    app = {
      address_prefixes = ["**********/24"]
    }
    data = {
      address_prefixes = ["**********/24"]
    }
  }
}
```

#### 5. Dev/Test Environment (devtest_a)
```hcl
devtest_a = {
  name                = "vnet-spoke-devtest-a-sea"
  resource_group_name = "rg-spoke-devtest-a-sea"
  address_space       = ["**********/16"]
  
  subnets = {
    development = {
      address_prefixes = ["**********/24"]
    }
    testing = {
      address_prefixes = ["**********/24"]
    }
    staging = {
      address_prefixes = ["**********/24"]
    }
  }
}
```

## 🔧 Cách Cấu Hình

### 1. Triển Khai Spoke Đầu Tiên (Product A)

```hcl
# terraform.tfvars cho spoke
root_id                     = "ewh"
spoke_name                  = "product_a"
location                    = "southeastasia"

# Hub network information (lấy từ connectivity module outputs)
hub_vnet_id                 = "/subscriptions/xxx/resourceGroups/rg-connectivity-sea/providers/Microsoft.Network/virtualNetworks/vnet-hub-sea"
hub_vnet_name               = "vnet-hub-sea"
hub_resource_group_name     = "rg-connectivity-sea"
hub_address_space           = "**********/16"
management_address_space    = "***********/21"

# Peering configuration
use_remote_gateways         = false
allow_gateway_transit       = true

# Route table (chưa có trong lần đầu)
enable_route_table_association = false

spoke_resources_tags = {
  deployedBy    = "CTS"
  type          = "spoke-resources"
  workload      = "product-a"
  environment   = "production"
  criticality   = "high"
}
```

### 2. Triển Khai Spoke Cho Microservices

```hcl
# Thay đổi spoke_name và tags
spoke_name = "product_microservice"

spoke_resources_tags = {
  deployedBy    = "CTS"
  type          = "spoke-resources"
  workload      = "microservices"
  environment   = "production"
  platform      = "kubernetes"
}
```

### 3. Triển Khai Spoke Cho UAT

```hcl
spoke_name = "uat"

spoke_resources_tags = {
  deployedBy    = "CTS"
  type          = "spoke-resources"
  workload      = "testing"
  environment   = "uat"
  criticality   = "medium"
}
```

### 4. Thêm Spoke Network Mới

Để thêm spoke network mới, chỉnh sửa `spoke/settings.spoke.tf`:

```hcl
locals {
  spoke_networks = {
    # ... existing spokes ...
    
    # New spoke
    product_d = {
      name                = "vnet-spoke-product-d-sea"
      resource_group_name = "rg-spoke-product-d-sea"
      address_space       = ["**********/16"]
      
      subnets = {
        web = {
          address_prefixes = ["**********/24"]
          service_endpoints = ["Microsoft.Storage"]
        }
        app = {
          address_prefixes = ["**********/24"]
          service_endpoints = ["Microsoft.Storage", "Microsoft.Sql"]
        }
      }
    }
  }
}
```

Sau đó cập nhật validation trong `spoke/variables.tf`:

```hcl
variable "spoke_name" {
  validation {
    condition = contains([
      "product_a", "product_microservice", "product_c",
      "uat", "devtest_a", "product_d"  # Thêm spoke mới
    ], var.spoke_name)
    error_message = "The spoke_name must be one of: product_a, product_microservice, product_c, uat, devtest_a, product_d"
  }
}
```

## 🛡️ Network Security Groups (NSGs)

Mỗi spoke tự động tạo NSGs với rules cơ bản:

### Inbound Rules
- Allow traffic từ Hub network
- Allow traffic từ Management subnet
- Deny all other inbound traffic

### Outbound Rules
- Allow traffic tới Hub network
- Allow traffic tới Internet (qua Firewall)
- Allow traffic tới Azure services

### Custom Rules Example

```hcl
# Trong spoke module, có thể thêm custom NSG rules
resource "azurerm_network_security_rule" "allow_https" {
  name                        = "Allow-HTTPS-Inbound"
  priority                    = 1000
  direction                   = "Inbound"
  access                      = "Allow"
  protocol                    = "Tcp"
  source_port_range          = "*"
  destination_port_range     = "443"
  source_address_prefix      = "Internet"
  destination_address_prefix = "**********/24"  # Web subnet
  resource_group_name        = azurerm_resource_group.spoke.name
  network_security_group_name = azurerm_network_security_group.web.name
}
```

## 🚀 Triển Khai

### 1. Triển Khai Spoke Networks (Khuyến Nghị)

Spoke module đã được tối ưu để tạo tất cả spoke networks được enable trong một lần triển khai:

```bash
# Bước 1: Bật spoke deployment trong terraform.tfvars
nano terraform.tfvars

# Thêm hoặc cập nhật dòng sau:
deploy_spokes = true

# Bước 2: Cấu hình spoke networks trong spoke/settings.spoke.tf
# Đặt enabled = true cho các spoke muốn triển khai
nano spoke/settings.spoke.tf

# Ví dụ:
# production = {
#   product_a = {
#     enabled = true    # Bật spoke này
#     ...
#   }
#   product_microservice = {
#     enabled = true    # Bật spoke này
#     ...
#   }
#   product_c = {
#     enabled = false   # Tắt spoke này
#     ...
#   }
# }

# Bước 3: Triển khai tất cả enabled spoke networks
terraform plan -target=module.spokes
terraform apply -target=module.spokes

# Hoặc triển khai tất cả cùng lúc
terraform plan
terraform apply
```

### 2. Triển Khai Spoke Riêng Biệt (Nâng Cao)

Nếu cần triển khai spoke riêng biệt hoặc tùy chỉnh nhiều hơn:

```bash
# Tạo thư mục cho spoke deployment
mkdir spoke-deployments
cd spoke-deployments

# Copy spoke module
cp -r ../spoke ./product-a-spoke

# Tạo terraform.tfvars cho spoke
cat > terraform.tfvars << EOF
root_id                     = "ewh"
spoke_name                  = "product_a"
location                    = "southeastasia"
hub_vnet_id                 = "$(terraform output -raw connectivity_outputs | jq -r '.hub_virtual_networks.primary.id')"
hub_vnet_name               = "vnet-hub-sea"
hub_resource_group_name     = "rg-connectivity-sea"
EOF

# Deploy spoke
terraform init
terraform plan
terraform apply
```

### 2. Triển Khai Multiple Spokes

```bash
# Script để deploy nhiều spokes
#!/bin/bash
SPOKES=("product_a" "product_microservice" "uat" "devtest_a")

for spoke in "${SPOKES[@]}"; do
  echo "Deploying spoke: $spoke"
  
  # Tạo thư mục riêng cho mỗi spoke
  mkdir -p "spoke-$spoke"
  cp -r ../spoke/* "spoke-$spoke/"
  
  # Tạo terraform.tfvars
  cat > "spoke-$spoke/terraform.tfvars" << EOF
root_id     = "ewh"
spoke_name  = "$spoke"
location    = "southeastasia"
# ... other variables
EOF
  
  # Deploy
  cd "spoke-$spoke"
  terraform init
  terraform apply -auto-approve
  cd ..
done
```

## 💰 Ước Tính Chi Phí

### Per Spoke Network

| Component | Chi Phí (USD/tháng) | Ghi Chú |
|-----------|---------------------|---------|
| VNet | Miễn phí | Chỉ tính traffic |
| VNet Peering | ~$22/TB | Inbound/outbound traffic |
| NSGs | Miễn phí | Unlimited rules |
| Route Tables | Miễn phí | Unlimited routes |

### Traffic Costs

- **VNet Peering**: $0.01/GB (same region)
- **Internet Egress**: $0.087/GB (first 5GB free)
- **Azure Services**: Varies by service

## 🔍 Kiểm Tra Sau Triển Khai

### 1. VNet và Subnets

```bash
# Kiểm tra spoke VNet
az network vnet show --name vnet-spoke-product-a-sea --resource-group rg-spoke-product-a-sea

# Kiểm tra subnets
az network vnet subnet list --vnet-name vnet-spoke-product-a-sea --resource-group rg-spoke-product-a-sea --output table
```

### 2. VNet Peering

```bash
# Kiểm tra peering status
az network vnet peering list --vnet-name vnet-spoke-product-a-sea --resource-group rg-spoke-product-a-sea --output table
```

### 3. NSGs

```bash
# Kiểm tra NSG rules
az network nsg list --resource-group rg-spoke-product-a-sea --output table
az network nsg rule list --nsg-name nsg-web --resource-group rg-spoke-product-a-sea --output table
```

### 4. Connectivity Tests

```bash
# Test connectivity từ spoke tới hub
# Test DNS resolution
# Test internet access qua firewall
```

## ⚠️ Lưu Ý Quan Trọng

### Address Space Planning

- Đảm bảo không overlap giữa các spokes
- Dự trữ address space cho tương lai
- Cân nhắc subnet sizing cho workloads

### Security

- Review NSG rules thường xuyên
- Sử dụng Application Security Groups cho complex rules
- Implement network monitoring

### Performance

- Monitor VNet peering bandwidth
- Optimize traffic flow qua hub
- Cân nhắc ExpressRoute cho high bandwidth

### Management

- Sử dụng consistent naming convention
- Tag resources properly
- Document network topology
