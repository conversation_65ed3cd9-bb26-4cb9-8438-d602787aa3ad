# Core Module - Management Groups và Policies

Core Module là nền tảng của Azure Landing Zone, chịu trách nhiệm tạo và quản lý Management Groups, Azure Policies, và Azure AD Groups.

## 🎯 Mục Đích

- Tạo cấu trúc Management Groups theo CAF Enterprise Scale
- Triển khai Azure Policies để đảm bảo tuân thủ và bảo mật
- Quản lý Azure AD Groups và phân quyền
- Thiết lập governance framework cho toàn bộ tenant

## 📋 Biến Cấu Hình

### Biến Bắt Buộc

```hcl
# Định danh tổ chức
root_id = "ewh"
# Mô tả: Tiền tố duy nhất cho tổ chức, đư<PERSON>c sử dụng để tạo tên tài nguyên
# Ví dụ: "ewh", "contoso", "fabrikam"

root_name = "EWH"
# Mô tả: Tên hiển thị cho root management group
# Ví dụ: "EWH", "Contoso Corporation", "Fabrikam Industries"

primary_location = "southeastasia"
# Mô tả: Vùng Azure chính để triển khai tài nguyên
# Các giá trị hợp lệ: "southeastasia", "eastasia", "eastus", "westus2", v.v.

# Subscription IDs
subscription_id_management = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
# Mô tả: Subscription ID cho tài nguyên quản lý

subscription_id_connectivity = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
# Mô tả: Subscription ID cho tài nguyên connectivity (có thể giống management)

subscription_id_identity = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
# Mô tả: Subscription ID cho tài nguyên identity (có thể giống management)
```

### Biến Tùy Chọn - Azure AD Groups

```hcl
azure_ad_groups = {
  "ewh-platform-admins" = {
    display_name         = "EWH Platform Administrators"
    description          = "Quản trị viên nền tảng EWH Landing Zone"
    security_enabled     = true
    assignable_to_role   = true
    mail_enabled         = false
    members              = [
      "<EMAIL>",
      "<EMAIL>"
    ]
    additional_owners    = ["<EMAIL>"]
    azure_roles          = [
      "Owner",
      "User Access Administrator"
    ]
    directory_roles      = []
  }
  
  "ewh-security-team" = {
    display_name         = "EWH Security Team"
    description          = "Đội ngũ bảo mật EWH"
    security_enabled     = true
    assignable_to_role   = true
    mail_enabled         = false
    members              = [
      "<EMAIL>",
      "<EMAIL>"
    ]
    additional_owners    = ["<EMAIL>"]
    azure_roles          = [
      "Security Reader",
      "Security Admin"
    ]
    directory_roles      = []
  }
  
  "ewh-network-admins" = {
    display_name         = "EWH Network Administrators"
    description          = "Quản trị viên mạng EWH"
    security_enabled     = true
    assignable_to_role   = true
    mail_enabled         = false
    members              = [
      "<EMAIL>",
      "<EMAIL>"
    ]
    additional_owners    = ["<EMAIL>"]
    azure_roles          = [
      "Network Contributor"
    ]
    directory_roles      = []
  }
}

# Cấu hình phân quyền
assign_roles_to_all_subscriptions = false
# Mô tả: Nếu true, sẽ phân quyền cho tất cả subscription trong tenant
# Khuyến nghị: false để kiểm soát tốt hơn

enable_directory_role_assignments = false
# Mô tả: Bật phân quyền Azure AD directory roles
# Yêu cầu: Quyền Privileged Role Administrator
# Khuyến nghị: false trừ khi thực sự cần thiết
```

## 🏗️ Tài Nguyên Được Tạo

### Management Groups

Core Module tạo cấu trúc Management Groups theo CAF Enterprise Scale:

```
Root Management Group (ewh)
├── Platform (ewh-platform)
│   ├── Management (ewh-management)
│   ├── Connectivity (ewh-connectivity)
│   └── Identity (ewh-identity)
├── Landing Zones (ewh-landingzones)
│   ├── Corp (ewh-landingzones-corp)
│   └── Online (ewh-landingzones-online)
├── Sandbox (ewh-sandbox)
└── Decommissioned (ewh-decommissioned)
```

### Azure Policies

Module triển khai các policy initiatives quan trọng:

- **Security Baseline**: Các policy bảo mật cơ bản
- **Monitoring**: Policy cho giám sát và logging
- **Networking**: Policy cho cấu hình mạng
- **Tagging**: Policy cho việc gắn thẻ tài nguyên

### Azure AD Groups

Tạo và quản lý các nhóm Azure AD với:
- Thành viên được chỉ định
- Phân quyền Azure roles
- Phân quyền Directory roles (tùy chọn)

## 🔧 Cách Cấu Hình

### 1. Cấu Hình Cơ Bản

Tạo file `terraform.tfvars` với cấu hình tối thiểu:

```hcl
# Cấu hình cơ bản
root_id                      = "ewh"
root_name                    = "EWH"
primary_location             = "southeastasia"
subscription_id_management   = "your-management-subscription-id"
subscription_id_connectivity = "your-connectivity-subscription-id"
subscription_id_identity     = "your-identity-subscription-id"
```

### 2. Cấu Hình Azure AD Groups

Thêm cấu hình Azure AD Groups vào `terraform.tfvars`:

```hcl
azure_ad_groups = {
  "platform-admins" = {
    display_name         = "Platform Administrators"
    description          = "Quản trị viên nền tảng"
    security_enabled     = true
    assignable_to_role   = true
    members              = ["<EMAIL>"]
    azure_roles          = ["Owner"]
  }
}
```

### 3. Cấu Hình Nâng Cao

Để bật các tính năng nâng cao:

```hcl
# Phân quyền toàn tenant
assign_roles_to_all_subscriptions = true

# Bật directory role assignments (cần quyền cao)
enable_directory_role_assignments = true
```

## ⚠️ Lưu Ý Quan Trọng

### Quyền Yêu Cầu

- **Owner** hoặc **User Access Administrator** ở cấp tenant root
- **Global Administrator** (nếu bật directory role assignments)
- Quyền tạo và quản lý Azure AD Groups

### Thứ Tự Triển Khai

Core Module phải được triển khai đầu tiên vì các module khác phụ thuộc vào:
- Management Groups structure
- Policy assignments
- Azure AD Groups

### Backup và Recovery

- Export cấu hình Management Groups trước khi thay đổi
- Backup danh sách thành viên Azure AD Groups
- Lưu trữ policy assignments để khôi phục nếu cần

## 🚀 Triển Khai

```bash
# Khởi tạo Terraform
terraform init

# Xem kế hoạch triển khai
terraform plan -target=module.core

# Triển khai Core Module
terraform apply -target=module.core

# Xác nhận triển khai thành công
terraform output core_outputs
```

## 🔍 Kiểm Tra Sau Triển Khai

1. **Management Groups**: Kiểm tra cấu trúc trong Azure Portal
2. **Policies**: Xác nhận policy assignments
3. **Azure AD Groups**: Kiểm tra nhóm và thành viên
4. **Subscriptions**: Xác nhận subscription được gán đúng management group

## 🆘 Troubleshooting

### Lỗi Phổ Biến

1. **Insufficient permissions**: Cần quyền cao hơn
2. **Subscription not found**: Kiểm tra subscription IDs
3. **Azure AD group creation failed**: Kiểm tra quyền Azure AD

### Giải Pháp

- Sử dụng `terraform plan` để xem trước thay đổi
- Kiểm tra logs chi tiết với `TF_LOG=DEBUG`
- Liên hệ Platform Team nếu cần hỗ trợ
