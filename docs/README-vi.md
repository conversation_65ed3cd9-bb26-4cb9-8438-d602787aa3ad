# Hướng Dẫn Sử Dụng EWH Landing Zone Terraform

Kho lưu trữ này chứa cấu hình Terraform để triển khai EWH Azure Landing Zone dựa trên kiến trúc Microsoft Cloud Adoption Framework (CAF) Enterprise Scale.

## 🏗️ Tổng Quan Kiến Trúc

Landing zone triển khai mô hình mạng hub-and-spoke với các thành phần sau:

- **Core Module**: Management Groups, Policies, và Azure AD Groups
- **Management Module**: Log Analytics, Security Center, và hạ tầng giám sát
- **Connectivity Module**: Hub virtual network, Azure Firewall, và bảo vệ DDoS
- **Spoke Module**: Mạng workload ứng dụng với peering tới hub

## 📋 Yêu Cầu Tiên Quyết

- **Azure CLI** đã được cài đặt và cấu hình (`az --version`)
- **Terraform** >= 1.0 (`terraform --version`)
- **Quyền Azure**: <PERSON>ai trò Owner hoặc User Access Administrator ở cấp tenant/management group
- **PowerShell** (khuyến nghị cho người dùng Windows để tránh lỗi đường dẫn)

## 🚀 Bắt Đầu Nhanh

1. **Clone repository này**
   ```bash
   git clone <repository-url>
   cd ewh-landingzone
   ```

2. **Cấu hình biến**
   ```bash
   cp terraform.tfvars.example terraform.tfvars
   # Chỉnh sửa terraform.tfvars với giá trị của bạn
   ```

3. **Khởi tạo và triển khai**
   ```bash
   terraform init
   terraform plan
   terraform apply
   ```

## ⚙️ Cấu Hình

### Biến Bắt Buộc

Cập nhật file `terraform.tfvars` với các giá trị cụ thể của bạn:

```hcl
# Cấu hình tổ chức
root_id              = "ewh"              # Tiền tố tổ chức của bạn
root_name            = "EWH"              # Tên hiển thị cho root management group
primary_location     = "southeastasia"    # Vùng Azure chính
secondary_location   = "eastasia"         # Vùng Azure phụ

# Subscription IDs (phải là Azure subscription IDs hợp lệ)
subscription_id_connectivity = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
subscription_id_management   = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
subscription_id_identity     = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"

# Cấu hình tính năng
enable_ddos_protection = false            # Bật DDoS Protection Standard
email_security_contact = "<EMAIL>"  # Email liên hệ bảo mật
```

### Biến Tùy Chọn

```hcl
# Thẻ tài nguyên
tags = {
  Environment = "Production"
  Owner       = "Platform Team"
  Project     = "Azure Landing Zone"
}

# Thẻ connectivity
connectivity_resources_tags = {
  Purpose = "Hub Network"
  Tier    = "Infrastructure"
}
```

## 📁 Cấu Trúc Module

```
├── core/                 # Management Groups và Policies
├── management/           # Log Analytics và Security Center
├── connectivity/         # Hạ tầng mạng Hub
├── spoke/               # Template mạng Spoke
├── main.tf              # Điều phối chính
├── variables.tf         # Định nghĩa biến
└── terraform.tfvars     # Giá trị cấu hình
```

## 🔄 Thứ Tự Triển Khai

Các module được triển khai theo thứ tự sau do phụ thuộc:

1. **Core Module**: Tạo management groups và policies
2. **Management Module**: Thiết lập hạ tầng giám sát và bảo mật
3. **Connectivity Module**: Triển khai mạng hub (phụ thuộc vào management)
4. **Spoke Modules**: Triển khai mạng ứng dụng (phụ thuộc vào connectivity)

## 🔒 Tính Năng Bảo Mật

- Tích hợp Microsoft Defender for Cloud
- Thực thi Azure Policy
- Network security groups và rules
- Azure Firewall để lọc traffic
- Log Analytics cho giám sát và cảnh báo

## 📊 Giám Sát và Tuân Thủ

- Logging tập trung với Log Analytics workspace
- Cảnh báo và thông báo bảo mật
- Giám sát tuân thủ policy
- Gắn thẻ tài nguyên để quản trị

## 📚 Hướng Dẫn Chi Tiết Từng Module

- [Core Module - Management Groups và Policies](./core-module.md)
- [Management Module - Log Analytics và Security Center](./management-module.md)
- [Connectivity Module - Hub Networking](./connectivity-module.md)
- [Spoke Module - Application Networks](./spoke-module.md)

## 🆘 Hỗ Trợ

Để được hỗ trợ và giải đáp thắc mắc, vui lòng liên hệ Platform Team tại <EMAIL>.

## 📄 Giấy Phép

Dự án này được cấp phép theo MIT License - xem file LICENSE để biết chi tiết.
